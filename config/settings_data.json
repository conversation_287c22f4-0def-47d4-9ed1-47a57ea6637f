/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "current": {
    "logo": "shopify://shop_images/upstep_logo.62b880e.svg",
    "type_body_font": "red_hat_display_n4",
    "type_subheading_font": "red_hat_display_n6",
    "type_heading_font": "trirong_n2",
    "type_accent_font": "trirong_n2",
    "type_size_paragraph": "14",
    "type_font_h1": "heading",
    "type_size_h1": "88",
    "type_line_height_h1": "display-tight",
    "type_letter_spacing_h1": "heading-normal",
    "type_case_h1": "none",
    "type_font_h2": "heading",
    "type_size_h2": "48",
    "type_letter_spacing_h2": "heading-normal",
    "type_case_h2": "none",
    "type_font_h3": "heading",
    "type_size_h3": "32",
    "type_line_height_h3": "heading-tight",
    "type_letter_spacing_h3": "heading-normal",
    "type_case_h3": "none",
    "type_font_h4": "subheading",
    "type_size_h4": "24",
    "type_size_h5": "14",
    "type_case_h5": "uppercase",
    "type_font_h6": "body",
    "type_size_h6": "12",
    "type_case_h6": "none",
    "page_width": "wide",
    "card_hover_effect": "scale",
    "badge_position": "top-right",
    "badge_corner_radius": 2,
    "badge_sale_color_scheme": "scheme-5",
    "badge_sold_out_color_scheme": "scheme-3",
    "badge_text_transform": "none",
    "primary_button_border_width": 0,
    "button_border_radius_primary": 0,
    "secondary_button_border_width": 1,
    "button_border_radius_secondary": 0,
    "cart_type": "drawer",
    "icon_stroke": "thin",
    "input_border_width": 1,
    "inputs_border_radius": 0,
    "popover_border_radius": 4,
    "popover_border": "none",
    "currency_code_enabled_product_pages": false,
    "currency_code_enabled_product_cards": false,
    "currency_code_enabled_cart_items": false,
    "card_title_case": "uppercase",
    "variant_swatch_width": 50,
    "variant_swatch_height": 20,
    "variant_swatch_radius": 0,
    "variant_button_border_width": 1,
    "variant_button_radius": 0,
    "variant_button_width": "equal-width-buttons",
    "content_for_index": [],
    "blocks": {
      "855628211100114053": {
        "type": "shopify://apps/klaviyo-email-marketing-sms/blocks/klaviyo-onsite-embed/2632fe16-c075-4321-a88b-50b567f42507",
        "disabled": false,
        "settings": {}
      },
      "7570552984445421165": {
        "type": "shopify://apps/yotpo-product-reviews/blocks/settings/eb7dfd7d-db44-4334-bc49-c893b51b36cf",
        "disabled": false,
        "settings": {}
      }
    },
    "color_schemes": {
      "scheme-1": {
        "settings": {
          "background": "#f9f8f6",
          "foreground_heading": "#000000",
          "foreground": "#000000",
          "primary": "#000000",
          "primary_hover": "#000000",
          "border": "#00000047",
          "shadow": "#000000",
          "primary_button_background": "#000000",
          "primary_button_text": "#ffffff",
          "primary_button_border": "#000000",
          "primary_button_hover_background": "#000000",
          "primary_button_hover_text": "#ffffff",
          "primary_button_hover_border": "#000000",
          "secondary_button_background": "#ffffff",
          "secondary_button_text": "#000000",
          "secondary_button_border": "#000000",
          "secondary_button_hover_background": "#dedede",
          "secondary_button_hover_text": "#000000",
          "secondary_button_hover_border": "#000000",
          "input_background": "#0000000d",
          "input_text_color": "#000000",
          "input_border_color": "rgba(0,0,0,0)",
          "input_hover_background": "#e6e6e6",
          "variant_background_color": "#f9f8f6",
          "variant_text_color": "#000000",
          "variant_border_color": "#e6e6e6",
          "variant_hover_background_color": "#f5f5f5",
          "variant_hover_text_color": "#000000",
          "variant_hover_border_color": "#e6e6e6",
          "selected_variant_background_color": "#000000",
          "selected_variant_text_color": "#ffffff",
          "selected_variant_border_color": "#000000",
          "selected_variant_hover_background_color": "#1a1a1a",
          "selected_variant_hover_text_color": "#ffffff",
          "selected_variant_hover_border_color": "#1a1a1a"
        }
      },
      "scheme-2": {
        "settings": {
          "background": "#ffffff",
          "foreground_heading": "#000000",
          "foreground": "#000000",
          "primary": "#000000",
          "primary_hover": "#000000",
          "border": "#e6e6e6",
          "shadow": "#000000",
          "primary_button_background": "#000000",
          "primary_button_text": "#ffffff",
          "primary_button_border": "#000000",
          "primary_button_hover_background": "#000f9f",
          "primary_button_hover_text": "#ffffff",
          "primary_button_hover_border": "#000f9f",
          "secondary_button_background": "#ffffff",
          "secondary_button_text": "#000000",
          "secondary_button_border": "#000000",
          "secondary_button_hover_background": "#ffffff",
          "secondary_button_hover_text": "#000f9f",
          "secondary_button_hover_border": "#000f9f",
          "input_background": "#ffffff",
          "input_text_color": "#000000",
          "input_border_color": "#000000",
          "input_hover_background": "#f5f5f5",
          "variant_background_color": "#ffffff",
          "variant_text_color": "#000000",
          "variant_border_color": "#e6e6e6",
          "variant_hover_background_color": "#f5f5f5",
          "variant_hover_text_color": "#000000",
          "variant_hover_border_color": "#e6e6e6",
          "selected_variant_background_color": "#000000",
          "selected_variant_text_color": "#ffffff",
          "selected_variant_border_color": "#000000",
          "selected_variant_hover_background_color": "#1a1a1a",
          "selected_variant_hover_text_color": "#ffffff",
          "selected_variant_hover_border_color": "#1a1a1a"
        }
      },
      "scheme-3": {
        "settings": {
          "background": "#f7f7f7",
          "foreground_heading": "#000000",
          "foreground": "#000000",
          "primary": "#000f9f",
          "primary_hover": "#000000",
          "border": "#d6d4d3",
          "shadow": "#000000",
          "primary_button_background": "#000000",
          "primary_button_text": "#ffffff",
          "primary_button_border": "#000000",
          "primary_button_hover_background": "#000000",
          "primary_button_hover_text": "#ffffff",
          "primary_button_hover_border": "#000000",
          "secondary_button_background": "#000000",
          "secondary_button_text": "#000f9f",
          "secondary_button_border": "#000f9f",
          "secondary_button_hover_background": "#efefef",
          "secondary_button_hover_text": "#000000",
          "secondary_button_hover_border": "#000000",
          "input_background": "#ffffff",
          "input_text_color": "#000000",
          "input_border_color": "#000000",
          "input_hover_background": "#f5f5f5",
          "variant_background_color": "#ffffff",
          "variant_text_color": "#000000",
          "variant_border_color": "#e6e6e6",
          "variant_hover_background_color": "#f5f5f5",
          "variant_hover_text_color": "#000000",
          "variant_hover_border_color": "#e6e6e6",
          "selected_variant_background_color": "#000000",
          "selected_variant_text_color": "#ffffff",
          "selected_variant_border_color": "#000000",
          "selected_variant_hover_background_color": "#1a1a1a",
          "selected_variant_hover_text_color": "#ffffff",
          "selected_variant_hover_border_color": "#1a1a1a"
        }
      },
      "scheme-4": {
        "settings": {
          "background": "#2e2e2e",
          "foreground_heading": "#ffffff",
          "foreground": "#ffffff",
          "primary": "#ffffff",
          "primary_hover": "#e1e1e1",
          "border": "#e1e1e1",
          "shadow": "#000000",
          "primary_button_background": "#ffffff",
          "primary_button_text": "#000000",
          "primary_button_border": "#ffffff",
          "primary_button_hover_background": "#e1e1e1",
          "primary_button_hover_text": "#000000",
          "primary_button_hover_border": "#e1e1e1",
          "secondary_button_background": "#000000",
          "secondary_button_text": "#ffffff",
          "secondary_button_border": "#ffffff",
          "secondary_button_hover_background": "#505050",
          "secondary_button_hover_text": "#ffffff",
          "secondary_button_hover_border": "#505050",
          "input_background": "#2e2e2e",
          "input_text_color": "#f9f8f6",
          "input_border_color": "#f9f8f6",
          "input_hover_background": "#1a1a1a",
          "variant_background_color": "#ffffff",
          "variant_text_color": "#000000",
          "variant_border_color": "#e6e6e6",
          "variant_hover_background_color": "#f5f5f5",
          "variant_hover_text_color": "#000000",
          "variant_hover_border_color": "#e6e6e6",
          "selected_variant_background_color": "#000000",
          "selected_variant_text_color": "#ffffff",
          "selected_variant_border_color": "#000000",
          "selected_variant_hover_background_color": "#1a1a1a",
          "selected_variant_hover_text_color": "#ffffff",
          "selected_variant_hover_border_color": "#1a1a1a"
        }
      },
      "scheme-5": {
        "settings": {
          "background": "#e4dfb5",
          "foreground_heading": "#000000",
          "foreground": "#000000",
          "primary": "#000000",
          "primary_hover": "#000000",
          "border": "#666666",
          "shadow": "#000000",
          "primary_button_background": "#ffffff",
          "primary_button_text": "#000000",
          "primary_button_border": "#ffffff",
          "primary_button_hover_background": "#dedede",
          "primary_button_hover_text": "#000000",
          "primary_button_hover_border": "#dedede",
          "secondary_button_background": "#000000",
          "secondary_button_text": "#ffffff",
          "secondary_button_border": "#ffffff",
          "secondary_button_hover_background": "#000000",
          "secondary_button_hover_text": "#acacac",
          "secondary_button_hover_border": "#acacac",
          "input_background": "#ffffff",
          "input_text_color": "#000000",
          "input_border_color": "#000000",
          "input_hover_background": "#f5f5f5",
          "variant_background_color": "#ffffff",
          "variant_text_color": "#000000",
          "variant_border_color": "#e6e6e6",
          "variant_hover_background_color": "#f5f5f5",
          "variant_hover_text_color": "#000000",
          "variant_hover_border_color": "#e6e6e6",
          "selected_variant_background_color": "#000000",
          "selected_variant_text_color": "#ffffff",
          "selected_variant_border_color": "#000000",
          "selected_variant_hover_background_color": "#1a1a1a",
          "selected_variant_hover_text_color": "#ffffff",
          "selected_variant_hover_border_color": "#1a1a1a"
        }
      },
      "scheme-6": {
        "settings": {
          "background": "rgba(0,0,0,0)",
          "foreground_heading": "#ffffff",
          "foreground": "#ffffff",
          "primary": "#ffffff",
          "primary_hover": "#acacac",
          "border": "rgba(0,0,0,0)",
          "shadow": "rgba(0,0,0,0)",
          "primary_button_background": "#ffffff",
          "primary_button_text": "#000000",
          "primary_button_border": "#ffffff",
          "primary_button_hover_background": "#dedede",
          "primary_button_hover_text": "#000000",
          "primary_button_hover_border": "#dedede",
          "secondary_button_background": "rgba(0,0,0,0)",
          "secondary_button_text": "#ffffff",
          "secondary_button_border": "#ffffff",
          "secondary_button_hover_background": "rgba(0,0,0,0)",
          "secondary_button_hover_text": "#acacac",
          "secondary_button_hover_border": "#acacac",
          "input_background": "#ffffff",
          "input_text_color": "#000000",
          "input_border_color": "#000000",
          "input_hover_background": "#f5f5f5",
          "variant_background_color": "#ffffff",
          "variant_text_color": "#000000",
          "variant_border_color": "#e6e6e6",
          "variant_hover_background_color": "#f5f5f5",
          "variant_hover_text_color": "#000000",
          "variant_hover_border_color": "#e6e6e6",
          "selected_variant_background_color": "#000000",
          "selected_variant_text_color": "#ffffff",
          "selected_variant_border_color": "#000000",
          "selected_variant_hover_background_color": "#1a1a1a",
          "selected_variant_hover_text_color": "#ffffff",
          "selected_variant_hover_border_color": "#1a1a1a"
        }
      },
      "scheme-58084d4c-a86e-4d0a-855e-a0966e5043f7": {
        "settings": {
          "background": "#00000008",
          "foreground_heading": "#000000",
          "foreground": "#000000",
          "primary": "#000000",
          "primary_hover": "#000000",
          "border": "#e6e6e6",
          "shadow": "#000000",
          "primary_button_background": "#000000",
          "primary_button_text": "#ffffff",
          "primary_button_border": "#000000",
          "primary_button_hover_background": "#000000cc",
          "primary_button_hover_text": "#ffffff",
          "primary_button_hover_border": "#000000cf",
          "secondary_button_background": "#efefef",
          "secondary_button_text": "#000000",
          "secondary_button_border": "#000f9f",
          "secondary_button_hover_background": "#dedede",
          "secondary_button_hover_text": "#000000",
          "secondary_button_hover_border": "#000000",
          "input_background": "#ffffff",
          "input_text_color": "#000000",
          "input_border_color": "#000000",
          "input_hover_background": "#f5f5f5",
          "variant_background_color": "#ffffff",
          "variant_text_color": "#000000",
          "variant_border_color": "#e6e6e6",
          "variant_hover_background_color": "#f5f5f5",
          "variant_hover_text_color": "#000000",
          "variant_hover_border_color": "#e6e6e6",
          "selected_variant_background_color": "#000000",
          "selected_variant_text_color": "#ffffff",
          "selected_variant_border_color": "#000000",
          "selected_variant_hover_background_color": "#1a1a1a",
          "selected_variant_hover_text_color": "#ffffff",
          "selected_variant_hover_border_color": "#1a1a1a"
        }
      },
      "scheme-9e6a4251-7c3c-44ef-8db0-43a15c30b893": {
        "settings": {
          "background": "#ffffff",
          "foreground_heading": "#1a1a1a",
          "foreground": "#1a1a1a",
          "primary": "#1a1a1a",
          "primary_hover": "#1a1a1a",
          "border": "#e0e0e0",
          "shadow": "#0000000d",
          "primary_button_background": "#0071f2",
          "primary_button_text": "#ffffff",
          "primary_button_border": "#0071f2",
          "primary_button_hover_background": "#005fcc",
          "primary_button_hover_text": "#ffffff",
          "primary_button_hover_border": "#005fcc",
          "secondary_button_background": "#ffffff",
          "secondary_button_text": "#0071f2",
          "secondary_button_border": "#0071f2",
          "secondary_button_hover_background": "#f0f8ff",
          "secondary_button_hover_text": "#005fcc",
          "secondary_button_hover_border": "#005fcc",
          "input_background": "#ffffff",
          "input_text_color": "#1a1a1a",
          "input_border_color": "#e0e0e0",
          "input_hover_background": "#f2f2f2",
          "variant_background_color": "#ffffff",
          "variant_text_color": "#1a1a1a",
          "variant_border_color": "#e0e0e0",
          "variant_hover_background_color": "#f5f5f5",
          "variant_hover_text_color": "#1a1a1a",
          "variant_hover_border_color": "#c9c9c9",
          "selected_variant_background_color": "#0071f2",
          "selected_variant_text_color": "#ffffff",
          "selected_variant_border_color": "#0071f2",
          "selected_variant_hover_background_color": "#005fcc",
          "selected_variant_hover_text_color": "#ffffff",
          "selected_variant_hover_border_color": "#005fcc"
        }
      }
    }
  },
  "presets": {
    "Vessel": {
      "color_schemes": {
        "scheme-1": {
          "settings": {
            "background": "#f9f8f6",
            "foreground_heading": "#000000",
            "foreground": "#000000",
            "primary": "#000000",
            "primary_hover": "#000000",
            "border": "#00000047",
            "shadow": "#000000",
            "primary_button_background": "#000000",
            "primary_button_text": "#ffffff",
            "primary_button_border": "#000000",
            "primary_button_hover_background": "#000000",
            "primary_button_hover_text": "#ffffff",
            "primary_button_hover_border": "#000000",
            "secondary_button_background": "#ffffff",
            "secondary_button_text": "#000000",
            "secondary_button_border": "#000000",
            "secondary_button_hover_background": "#dedede",
            "secondary_button_hover_text": "#000000",
            "secondary_button_hover_border": "#000000",
            "input_background": "#0000000d",
            "input_text_color": "#000000",
            "input_border_color": "rgba(0,0,0,0)",
            "input_hover_background": "#e6e6e6",
            "variant_background_color": "#f9f8f6",
            "variant_text_color": "#000000",
            "variant_border_color": "#e6e6e6",
            "variant_hover_background_color": "#f5f5f5",
            "variant_hover_text_color": "#000000",
            "variant_hover_border_color": "#e6e6e6",
            "selected_variant_background_color": "#000000",
            "selected_variant_text_color": "#ffffff",
            "selected_variant_border_color": "#000000",
            "selected_variant_hover_background_color": "#1a1a1a",
            "selected_variant_hover_text_color": "#ffffff",
            "selected_variant_hover_border_color": "#1a1a1a"
          }
        },
        "scheme-2": {
          "settings": {
            "background": "#ffffff",
            "foreground_heading": "#000000",
            "foreground": "#000000",
            "primary": "#000000",
            "primary_hover": "#000000",
            "border": "#e6e6e6",
            "shadow": "#000000",
            "primary_button_background": "#000000",
            "primary_button_text": "#ffffff",
            "primary_button_border": "#000000",
            "primary_button_hover_background": "#000f9f",
            "primary_button_hover_text": "#ffffff",
            "primary_button_hover_border": "#000f9f",
            "secondary_button_background": "#ffffff",
            "secondary_button_text": "#000000",
            "secondary_button_border": "#000000",
            "secondary_button_hover_background": "#ffffff",
            "secondary_button_hover_text": "#000f9f",
            "secondary_button_hover_border": "#000f9f",
            "input_background": "#ffffff",
            "input_text_color": "#000000",
            "input_border_color": "#000000",
            "input_hover_background": "#f5f5f5",
            "variant_background_color": "#ffffff",
            "variant_text_color": "#000000",
            "variant_border_color": "#e6e6e6",
            "variant_hover_background_color": "#f5f5f5",
            "variant_hover_text_color": "#000000",
            "variant_hover_border_color": "#e6e6e6",
            "selected_variant_background_color": "#000000",
            "selected_variant_text_color": "#ffffff",
            "selected_variant_border_color": "#000000",
            "selected_variant_hover_background_color": "#1a1a1a",
            "selected_variant_hover_text_color": "#ffffff",
            "selected_variant_hover_border_color": "#1a1a1a"
          }
        },
        "scheme-3": {
          "settings": {
            "background": "#f7f7f7",
            "foreground_heading": "#000000",
            "foreground": "#000000",
            "primary": "#000f9f",
            "primary_hover": "#000000",
            "border": "#d6d4d3",
            "shadow": "#000000",
            "primary_button_background": "#000000",
            "primary_button_text": "#ffffff",
            "primary_button_border": "#000000",
            "primary_button_hover_background": "#000000",
            "primary_button_hover_text": "#ffffff",
            "primary_button_hover_border": "#000000",
            "secondary_button_background": "#000000",
            "secondary_button_text": "#000f9f",
            "secondary_button_border": "#000f9f",
            "secondary_button_hover_background": "#efefef",
            "secondary_button_hover_text": "#000000",
            "secondary_button_hover_border": "#000000",
            "input_background": "#ffffff",
            "input_text_color": "#000000",
            "input_border_color": "#000000",
            "input_hover_background": "#f5f5f5",
            "variant_background_color": "#ffffff",
            "variant_text_color": "#000000",
            "variant_border_color": "#e6e6e6",
            "variant_hover_background_color": "#f5f5f5",
            "variant_hover_text_color": "#000000",
            "variant_hover_border_color": "#e6e6e6",
            "selected_variant_background_color": "#000000",
            "selected_variant_text_color": "#ffffff",
            "selected_variant_border_color": "#000000",
            "selected_variant_hover_background_color": "#1a1a1a",
            "selected_variant_hover_text_color": "#ffffff",
            "selected_variant_hover_border_color": "#1a1a1a"
          }
        },
        "scheme-4": {
          "settings": {
            "background": "#2e2e2e",
            "foreground_heading": "#ffffff",
            "foreground": "#ffffff",
            "primary": "#ffffff",
            "primary_hover": "#e1e1e1",
            "border": "#e1e1e1",
            "shadow": "#000000",
            "primary_button_background": "#ffffff",
            "primary_button_text": "#000000",
            "primary_button_border": "#ffffff",
            "primary_button_hover_background": "#e1e1e1",
            "primary_button_hover_text": "#000000",
            "primary_button_hover_border": "#e1e1e1",
            "secondary_button_background": "#000000",
            "secondary_button_text": "#ffffff",
            "secondary_button_border": "#ffffff",
            "secondary_button_hover_background": "#505050",
            "secondary_button_hover_text": "#ffffff",
            "secondary_button_hover_border": "#505050",
            "input_background": "#2e2e2e",
            "input_text_color": "#f9f8f6",
            "input_border_color": "#f9f8f6",
            "input_hover_background": "#1a1a1a",
            "variant_background_color": "#ffffff",
            "variant_text_color": "#000000",
            "variant_border_color": "#e6e6e6",
            "variant_hover_background_color": "#f5f5f5",
            "variant_hover_text_color": "#000000",
            "variant_hover_border_color": "#e6e6e6",
            "selected_variant_background_color": "#000000",
            "selected_variant_text_color": "#ffffff",
            "selected_variant_border_color": "#000000",
            "selected_variant_hover_background_color": "#1a1a1a",
            "selected_variant_hover_text_color": "#ffffff",
            "selected_variant_hover_border_color": "#1a1a1a"
          }
        },
        "scheme-5": {
          "settings": {
            "background": "#e4dfb5",
            "foreground_heading": "#000000",
            "foreground": "#000000",
            "primary": "#000000",
            "primary_hover": "#000000",
            "border": "#666666",
            "shadow": "#000000",
            "primary_button_background": "#ffffff",
            "primary_button_text": "#000000",
            "primary_button_border": "#ffffff",
            "primary_button_hover_background": "#dedede",
            "primary_button_hover_text": "#000000",
            "primary_button_hover_border": "#dedede",
            "secondary_button_background": "#000000",
            "secondary_button_text": "#ffffff",
            "secondary_button_border": "#ffffff",
            "secondary_button_hover_background": "#000000",
            "secondary_button_hover_text": "#acacac",
            "secondary_button_hover_border": "#acacac",
            "input_background": "#ffffff",
            "input_text_color": "#000000",
            "input_border_color": "#000000",
            "input_hover_background": "#f5f5f5",
            "variant_background_color": "#ffffff",
            "variant_text_color": "#000000",
            "variant_border_color": "#e6e6e6",
            "variant_hover_background_color": "#f5f5f5",
            "variant_hover_text_color": "#000000",
            "variant_hover_border_color": "#e6e6e6",
            "selected_variant_background_color": "#000000",
            "selected_variant_text_color": "#ffffff",
            "selected_variant_border_color": "#000000",
            "selected_variant_hover_background_color": "#1a1a1a",
            "selected_variant_hover_text_color": "#ffffff",
            "selected_variant_hover_border_color": "#1a1a1a"
          }
        },
        "scheme-6": {
          "settings": {
            "background": "rgba(0,0,0,0)",
            "foreground_heading": "#ffffff",
            "foreground": "#ffffff",
            "primary": "#ffffff",
            "primary_hover": "#acacac",
            "border": "rgba(0,0,0,0)",
            "shadow": "rgba(0,0,0,0)",
            "primary_button_background": "#ffffff",
            "primary_button_text": "#000000",
            "primary_button_border": "#ffffff",
            "primary_button_hover_background": "#dedede",
            "primary_button_hover_text": "#000000",
            "primary_button_hover_border": "#dedede",
            "secondary_button_background": "rgba(0,0,0,0)",
            "secondary_button_text": "#ffffff",
            "secondary_button_border": "#ffffff",
            "secondary_button_hover_background": "rgba(0,0,0,0)",
            "secondary_button_hover_text": "#acacac",
            "secondary_button_hover_border": "#acacac",
            "input_background": "#ffffff",
            "input_text_color": "#000000",
            "input_border_color": "#000000",
            "input_hover_background": "#f5f5f5",
            "variant_background_color": "#ffffff",
            "variant_text_color": "#000000",
            "variant_border_color": "#e6e6e6",
            "variant_hover_background_color": "#f5f5f5",
            "variant_hover_text_color": "#000000",
            "variant_hover_border_color": "#e6e6e6",
            "selected_variant_background_color": "#000000",
            "selected_variant_text_color": "#ffffff",
            "selected_variant_border_color": "#000000",
            "selected_variant_hover_background_color": "#1a1a1a",
            "selected_variant_hover_text_color": "#ffffff",
            "selected_variant_hover_border_color": "#1a1a1a"
          }
        },
        "scheme-58084d4c-a86e-4d0a-855e-a0966e5043f7": {
          "settings": {
            "background": "#00000008",
            "foreground_heading": "#000000",
            "foreground": "#000000",
            "primary": "#000000",
            "primary_hover": "#000000",
            "border": "#e6e6e6",
            "shadow": "#000000",
            "primary_button_background": "#000000",
            "primary_button_text": "#ffffff",
            "primary_button_border": "#000000",
            "primary_button_hover_background": "#000000cc",
            "primary_button_hover_text": "#ffffff",
            "primary_button_hover_border": "#000000cf",
            "secondary_button_background": "#efefef",
            "secondary_button_text": "#000000",
            "secondary_button_border": "#000f9f",
            "secondary_button_hover_background": "#dedede",
            "secondary_button_hover_text": "#000000",
            "secondary_button_hover_border": "#000000",
            "input_background": "#ffffff",
            "input_text_color": "#000000",
            "input_border_color": "#000000",
            "input_hover_background": "#f5f5f5",
            "variant_background_color": "#ffffff",
            "variant_text_color": "#000000",
            "variant_border_color": "#e6e6e6",
            "variant_hover_background_color": "#f5f5f5",
            "variant_hover_text_color": "#000000",
            "variant_hover_border_color": "#e6e6e6",
            "selected_variant_background_color": "#000000",
            "selected_variant_text_color": "#ffffff",
            "selected_variant_border_color": "#000000",
            "selected_variant_hover_background_color": "#1a1a1a",
            "selected_variant_hover_text_color": "#ffffff",
            "selected_variant_hover_border_color": "#1a1a1a"
          }
        }
      }
    }
  }
}

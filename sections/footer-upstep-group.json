/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "type": "footer",
  "name": "Footer Upstep",
  "sections": {
    "175334045238cae369": {
      "type": "_blocks",
      "blocks": {
        "klaviyo_email_marketing_sms_form_embed_block_gEp47h": {
          "type": "shopify://apps/klaviyo-email-marketing-sms/blocks/form-embed-block/2632fe16-c075-4321-a88b-50b567f42507",
          "settings": {
            "formId": "VBuVjz"
          }
        }
      },
      "block_order": [
        "klaviyo_email_marketing_sms_form_embed_block_gEp47h"
      ],
      "settings": {
        "content_direction": "column",
        "vertical_on_mobile": true,
        "horizontal_alignment": "flex-start",
        "vertical_alignment": "center",
        "align_baseline": false,
        "horizontal_alignment_flex_direction_column": "flex-start",
        "vertical_alignment_flex_direction_column": "center",
        "gap": 12,
        "section_width": "page-width",
        "section_height": "",
        "section_height_custom": 50,
        "color_scheme": "",
        "background_media": "none",
        "video_position": "cover",
        "background_image_position": "cover",
        "border": "none",
        "border_width": 1,
        "border_opacity": 100,
        "border_radius": 0,
        "toggle_overlay": false,
        "overlay_color": "#00000026",
        "overlay_style": "solid",
        "gradient_direction": "to top",
        "padding-block-start": 0,
        "padding-block-end": 0
      }
    },
    "footer_upstep": {
      "type": "footer-upstep",
      "blocks": {
        "menu_company": {
          "type": "menu_column",
          "settings": {
            "heading": "Company",
            "menu": "main-menu",
            "open_in_new_tab": false
          }
        },
        "menu_education": {
          "type": "menu_column",
          "settings": {
            "heading": "Education",
            "menu": "main-menu",
            "open_in_new_tab": false
          }
        },
        "menu_support": {
          "type": "menu_column",
          "settings": {
            "heading": "Support",
            "menu": "main-menu",
            "open_in_new_tab": false
          }
        },
        "payment_visa": {
          "type": "payment_icon",
          "settings": {
            "icon": "shopify://shop_images/cc-visa-brands-solid.svg",
            "icon_type": "VISA",
            "alt_text": "Visa"
          }
        },
        "payment_icon_TBwcgK": {
          "type": "payment_icon",
          "settings": {
            "icon_type": "VISA",
            "alt_text": "Payment method"
          }
        },
        "payment_mastercard": {
          "type": "payment_icon",
          "settings": {
            "icon": "shopify://shop_images/cc-mastercard-brands-solid-full.svg",
            "icon_type": "MC",
            "alt_text": "Mastercard"
          }
        },
        "payment_amex": {
          "type": "payment_icon",
          "settings": {
            "icon_type": "AMEX",
            "alt_text": "American Express"
          }
        },
        "payment_paypal": {
          "type": "payment_icon",
          "settings": {
            "icon": "shopify://shop_images/paypal-brands-solid-full.svg",
            "icon_type": "PAYPAL",
            "alt_text": "PayPal"
          }
        },
        "payment_gpay": {
          "type": "payment_icon",
          "settings": {
            "icon_type": "GPAY",
            "alt_text": "Google Pay"
          }
        },
        "payment_apple": {
          "type": "payment_icon",
          "settings": {
            "icon": "shopify://shop_images/apple-pay-brands-solid-full.svg",
            "icon_type": "APPLE",
            "alt_text": "Apple Pay"
          }
        },
        "payment_discover": {
          "type": "payment_icon",
          "settings": {
            "icon_type": "DISC",
            "alt_text": "Discover"
          }
        },
        "payment_fsa": {
          "type": "payment_icon",
          "settings": {
            "icon_type": "FSA",
            "alt_text": "FSA"
          }
        }
      },
      "block_order": [
        "menu_company",
        "menu_education",
        "menu_support",
        "payment_visa",
        "payment_icon_TBwcgK",
        "payment_mastercard",
        "payment_amex",
        "payment_paypal",
        "payment_gpay",
        "payment_apple",
        "payment_discover",
        "payment_fsa"
      ],
      "settings": {
        "show_logo": true,
        "show_newsletter": true,
        "newsletter_heading": "Step with us! and get all our offers and benefits straight to your email!",
        "newsletter_text": "",
        "newsletter_placeholder": "Email",
        "newsletter_button_text": "SUBMIT",
        "newsletter_input_bg": "#ffffff",
        "newsletter_input_text_color": "#333333",
        "newsletter_placeholder_color": "#999999",
        "newsletter_button_bg": "#333333",
        "newsletter_button_text_color": "#ffffff",
        "newsletter_button_hover_bg": "#555555",
        "newsletter_border_color": "#e0e0e0",
        "newsletter_border_radius": 2,
        "newsletter_input_height": 40,
        "newsletter_input_font_size": 0.9,
        "newsletter_button_font_size": 0.9,
        "newsletter_button_min_width": 100,
        "copyright_text": "Upstep",
        "copyright_suffix": "All Rights Reserved.",
        "show_policy_links": true,
        "privacy_policy_link": "",
        "terms_conditions_link": "",
        "accessibility_link": "",
        "show_payment_icons": true,
        "background_color": "#e6e6e6",
        "text_color": "",
        "heading_color": "",
        "link_color": "",
        "border_color": "",
        "section_width": "page-width",
        "color_scheme": "",
        "padding-block-start": 32,
        "padding-block-end": 24
      }
    }
  },
  "order": [
    "175334045238cae369",
    "footer_upstep"
  ]
}

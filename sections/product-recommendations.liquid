
<script
  src="{{ 'product-recommendations.js' | asset_url }}"
  type="module"
></script>

{% liquid
  case section.settings.layout_type
    when 'grid'
      assign classes = 'resource-list--grid'
    when 'carousel'
      assign classes = 'resource-list__carousel'
  endcase

  capture styles
    echo '--column-count-mobile: ' | append: section.settings.mobile_columns | append: ';'
    echo '--resource-list-column-gap-desktop: ' | append: section.settings.columns_gap | append: 'px;'
    echo '--resource-list-row-gap-desktop: ' | append: section.settings.rows_gap | append: 'px;'
    echo '--resource-list-columns: repeat(' | append: section.settings.columns | append: ', 1fr);'
    echo '--resource-list-columns-mobile: repeat(' | append: section.settings.mobile_columns | append: ', 1fr);'
    echo '--resource-list-column-gap-desktop: ' | append: section.settings.columns_gap | append: 'px;'
    echo '--column-count: ' | append: section.settings.columns | append: ';'
    echo '--column-count-mobile: ' | append: section.settings.mobile_columns | append: ';'
  endcapture
%}

<product-recommendations
  id="product-recommendations-{{ section.id }}"
  class="product-recommendations"
  data-url="{{ routes.product_recommendations_url }}?limit={{ section.settings.max_products }}"
  data-section-id="{{ section.id }}"
  data-product-id="{{ section.settings.product.id | default: product.id }}"
  data-intent="{{ section.settings.recommendation_type }}"
  data-testid="product-recommendations-section"
  data-recommendations-performed="{{ recommendations.performed }}"
  {{ section.shopify_attributes }}
>
  <div class="section-background color-{{ section.settings.color_scheme }}"></div>
  <div
    class="
      section
      section--{{ section.settings.section_width }}
      color-{{ section.settings.color_scheme }}
      section-resource-list
      spacing-style
      gap-style
    "
    style="
      {%  render 'spacing-style', settings: section.settings %}
      {%  render 'gap-style', value: section.settings.gap %}
      {{ styles }}
    "
  >
    <div class="section-resource-list__content">
      {% content_for 'blocks' %}
    </div>

    {%- if recommendations.performed or section.settings.product == blank -%}
      {% liquid
        if section.settings.product == blank
          assign products = null
          if request.design_mode or request.visual_preview_mode
            # Onboarding mode: Show placeholder products
            for i in (1..section.settings.max_products)
              assign products = products | append: ', '
              assign products = products | split: ','
            endfor
          endif
        elsif recommendations.performed and recommendations.products_count == 0
          # No recommendations found, pull from catalog
          if section.settings.recommendation_type == 'related'
            assign products = collections.all.products | reject: 'id', section.settings.product.id
          elsif section.settings.recommendation_type == 'complementary'
            # Do not recommend the All collection as complementary products
            assign products = null
          endif

        else
          assign products = recommendations.products
        endif
      %}
      {% capture list_items %}
        {% for product in products limit: section.settings.max_products %}
          <div class="resource-list__item">
            {% content_for 'block', type: '_product-card', id: 'static-product-card', closest.product: product %}
          </div>
          {% unless forloop.last %}
            <!--@list/split-->
          {% endunless %}
        {% endfor %}
      {% endcapture %}

      {% liquid
        # Create an array from the list items to be used in the carousel
        assign slide_content = list_items | strip
        assign slides = slide_content | split: '<!--@list/split-->'

        if products != blank and products.size > 0
          assign has_recommendations = 'true'
        else
          assign has_recommendations = 'false'
        endif
      %}

      <div
        class="
          resource-list
          {% if section.settings.layout_type == 'carousel' %}
            force-full-width
          {% endif %}
          {% if section.settings.carousel_on_mobile and section.settings.layout_type != 'carousel' %}
            hidden--mobile
          {% endif %}
          {{ classes }}
        "
        {% if section.settings.layout_type == 'grid' %}
          data-testid="resource-list-grid"
        {% endif %}
        data-has-recommendations="{{ has_recommendations }}"
      >
        {% case section.settings.layout_type %}
          {% when 'grid' %}
            {{ list_items }}
          {% when 'carousel' %}
            {% render 'resource-list-carousel',
              ref: 'resourceListCarousel',
              slides: slides,
              slide_count: recommendations.products.size,
              settings: section.settings
            %}
        {% endcase %}
      </div>

      {% if section.settings.carousel_on_mobile and section.settings.layout_type != 'carousel' %}
        {% liquid
          assign mobile_carousel_gap = section.settings.columns_gap
        %}
        <div
          class="
            resource-list
            hidden--desktop
            force-full-width
          "
          style="
            --resource-list-gap: {{ mobile_carousel_gap }}px;
            --column-count: {{ section.settings.columns }};
          "
        >
          {% render 'resource-list-carousel',
            ref: 'resourceListCarouselMobile',
            slides: slides,
            slide_count: recommendations.products.size,
            settings: section.settings
          %}
        </div>
      {% endif %}
    {%- else -%}
      <div
        class="resource-list resource-list--grid"
      >
        {% for i in (1..section.settings.columns) %}
          <div
            class="product-recommendations__skeleton-item"
            aria-label="{{ 'accessibility.loading_product_recommendations' | t }}"
          ></div>
        {% endfor %}
      </div>
    {%- endif -%}
  </div>
</product-recommendations>

{% stylesheet %}
  .product-recommendations__skeleton-item {
    aspect-ratio: 3 / 4;
    background-color: var(--color-foreground);
    opacity: var(--skeleton-opacity);
    border-radius: 4px;
  }

  @media screen and (max-width: 749px) {
    .product-recommendations__skeleton-item:nth-child(2n + 1) {
      display: none;
    }
  }

  product-recommendations:has([data-has-recommendations='false']) {
    display: none;
  }
{% endstylesheet %}

{% schema %}
{
  "name": "t:names.product_recommendations",
  "disabled_on": {
    "groups": ["header", "footer"]
  },
  "blocks": [
    {
      "type": "@theme"
    },
    {
      "type": "@app"
    },
    {
      "type": "text"
    },
    {
      "type": "icon"
    },
    {
      "type": "image"
    },
    {
      "type": "button"
    },
    {
      "type": "video"
    },
    {
      "type": "group"
    },
    {
      "type": "spacer"
    },
    {
      "type": "_divider"
    }
  ],
  "settings": [
    {
      "type": "product",
      "id": "product",
      "label": "t:settings.product"
    },
    {
      "type": "select",
      "id": "recommendation_type",
      "label": "t:settings.type",
      "options": [
        {
          "value": "related",
          "label": "t:options.related"
        },
        {
          "value": "complementary",
          "label": "t:options.complementary"
        }
      ],
      "default": "related"
    },
    {
      "type": "paragraph",
      "content": "t:content.complementary_products"
    },
    {
      "type": "header",
      "content": "t:content.cards_layout"
    },
    {
      "type": "select",
      "id": "layout_type",
      "label": "t:settings.layout_style",
      "options": [
        {
          "value": "grid",
          "label": "t:options.grid"
        },
        {
          "value": "carousel",
          "label": "t:options.carousel"
        }
      ],
      "default": "grid"
    },
    {
      "type": "checkbox",
      "id": "carousel_on_mobile",
      "label": "t:settings.carousel_on_mobile",
      "default": false,
      "visible_if": "{{ section.settings.layout_type == 'grid' }}"
    },
    {
      "type": "range",
      "id": "max_products",
      "label": "t:settings.product_count",
      "min": 3,
      "max": 10,
      "step": 1,
      "default": 4
    },
    {
      "type": "range",
      "id": "columns",
      "label": "t:settings.columns",
      "min": 1,
      "max": 8,
      "step": 1,
      "default": 4
    },
    {
      "type": "select",
      "id": "mobile_columns",
      "label": "t:settings.mobile_columns",
      "options": [
        {
          "value": "1",
          "label": "t:options.one_number"
        },
        {
          "value": "2",
          "label": "t:options.two_number"
        }
      ],
      "default": "2",
      "visible_if": "{{ section.settings.layout_type == 'grid' and section.settings.carousel_on_mobile == false }}"
    },
    {
      "type": "range",
      "id": "columns_gap",
      "label": "t:settings.horizontal_gap",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 16,
      "visible_if": "{{ section.settings.layout_type == 'grid' or section.settings.layout_type == 'carousel' }}"
    },
    {
      "type": "range",
      "id": "rows_gap",
      "label": "t:settings.vertical_gap",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 16,
      "visible_if": "{{ section.settings.layout_type == 'grid' }}"
    },
    {
      "type": "header",
      "content": "t:content.carousel_navigation",
      "visible_if": "{{ section.settings.layout_type == 'carousel' or section.settings.carousel_on_mobile == true }}"
    },
    {
      "type": "select",
      "id": "icons_style",
      "label": "t:settings.icon",
      "options": [
        {
          "value": "arrow",
          "label": "t:options.arrows"
        },
        {
          "value": "chevron",
          "label": "t:options.chevrons"
        },
        {
          "value": "arrows_large",
          "label": "t:options.arrows_large"
        },
        {
          "value": "chevron_large",
          "label": "t:options.chevron_large"
        },
        {
          "value": "none",
          "label": "t:options.none"
        }
      ],
      "default": "arrow",
      "visible_if": "{{ section.settings.layout_type == 'carousel' or section.settings.carousel_on_mobile == true }}"
    },
    {
      "type": "select",
      "id": "icons_shape",
      "label": "t:settings.icon_background",
      "options": [
        {
          "value": "none",
          "label": "t:options.none"
        },
        {
          "value": "circle",
          "label": "t:options.circle"
        },
        {
          "value": "square",
          "label": "t:options.square"
        }
      ],
      "default": "none",
      "visible_if": "{{ section.settings.icons_style != 'none' and section.settings.layout_type == 'carousel' or section.settings.carousel_on_mobile == true }}"
    },
    {
      "type": "header",
      "content": "t:content.section_layout"
    },
    {
      "type": "select",
      "id": "section_width",
      "label": "t:settings.width",
      "options": [
        {
          "value": "page-width",
          "label": "t:options.page"
        },
        {
          "value": "full-width",
          "label": "t:options.full"
        }
      ],
      "default": "page-width"
    },
    {
      "type": "range",
      "id": "gap",
      "label": "t:settings.gap",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 12
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:settings.color_scheme",
      "default": "scheme-1"
    },
    {
      "type": "header",
      "content": "t:content.padding"
    },
    {
      "type": "range",
      "id": "padding-block-start",
      "label": "t:settings.top",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding-block-end",
      "label": "t:settings.bottom",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    }
  ],
  "presets": [
    {
      "name": "t:names.product_recommendations",
      "category": "t:categories.products",
      "settings": {
        "product": "{{ closest.product }}",
        "recommendation_type": "related",
        "layout_type": "grid",
        "carousel_on_mobile": false,
        "max_products": 4,
        "columns": 4,
        "mobile_columns": "2",
        "columns_gap": 12,
        "rows_gap": 36,
        "icons_style": "arrow",
        "icons_shape": "none",
        "section_width": "page-width",
        "gap": 28,
        "color_scheme": "scheme-1",
        "padding-block-start": 48,
        "padding-block-end": 48
      },
      "blocks": {
        "header": {
          "type": "text",
          "name": "t:names.header",
          "settings": {
            "text": "<h3>Related products</h3>"
          }
        },
        "static-product-card": {
          "type": "_product-card",
          "name": "t:names.product_card",
          "static": true,
          "settings": {},
          "blocks": {
            "card-gallery": {
              "type": "_product-card-gallery",
              "name": "t:names.product_card_media"
            },
            "group": {
              "type": "_product-card-group",
              "settings": {
                "content_direction": "column",
                "gap": 4
              },
              "blocks": {
                "text": {
                  "type": "product-title",
                  "settings": {
                    "type_preset": "h5"
                  }
                },
                "price": {
                  "type": "price",
                  "settings": {
                    "show_tax_info": false
                  }
                },
                "swatches": {
                  "type": "swatches"
                }
              },
              "block_order": ["text", "price", "swatches"]
            }
          },
          "block_order": ["card-gallery", "group"]
        }
      },
      "block_order": ["header"]
    }
  ]
}
{% endschema %}

{% comment %}
  Why Upstep Section - Customizable benefits section with icons
  Features main content area with button and grid of benefits
{% endcomment %}

<div class="section-background color-{{ section.settings.color_scheme }}"></div>
<div
  class="
    why-upstep-section spacing-style section section--{{ section.settings.section_width }} color-{{ section.settings.color_scheme }}
  "
  style="
    {% render 'spacing-style', settings: section.settings %}
    --why-upstep-bg-color: {{ section.settings.background_color | default: '#ffffff' }};
    --why-upstep-heading-color: {{ section.settings.heading_color | default: 'rgba(var(--color-foreground), 1)' }};
    --why-upstep-text-color: {{ section.settings.text_color | default: 'rgba(var(--color-foreground), 0.75)' }};
    --why-upstep-button-bg: {{ section.settings.button_background | default: '#0066ff' }};
    --why-upstep-button-color: {{ section.settings.button_color | default: '#ffffff' }};
    --why-upstep-benefit-title-color: {{ section.settings.benefit_title_color | default: 'rgba(var(--color-foreground), 1)' }};
    --why-upstep-benefit-text-color: {{ section.settings.benefit_text_color | default: 'rgba(var(--color-foreground), 0.75)' }};
    --why-upstep-heading-weight: {{ section.settings.heading_weight | default: 600 }};
    --why-upstep-heading-size: {{ section.settings.heading_size | default: 32 }}px;
    --why-upstep-description-size: {{ section.settings.description_size | default: 16 }}px;
    --why-upstep-benefit-title-size: {{ section.settings.benefit_title_size | default: 18 }}px;
    --why-upstep-benefit-text-size: {{ section.settings.benefit_text_size | default: 14 }}px;
    --why-upstep-icon-size: {{ section.settings.icon_size | default: 60 }}px;
    background: var(--why-upstep-bg-color);
  "
>
  <div class="why-upstep-section__container">
    <!-- Main Content Area -->
    <div class="why-upstep-section__main">
      {% if section.settings.heading != blank %}
        <h2 class="why-upstep-section__heading">{{ section.settings.heading }}</h2>
      {% endif %}
      
      {% if section.settings.description != blank %}
        <div class="why-upstep-section__description">{{ section.settings.description }}</div>
      {% endif %}
      
      {% if section.settings.button_text != blank %}
        <div class="why-upstep-section__button-wrapper">
          <a 
            href="{{ section.settings.button_url | default: '#' }}" 
            class="why-upstep-section__button"
            {% if section.settings.button_open_new_tab %}target="_blank"{% endif %}
          >
            {{ section.settings.button_text }}
          </a>
          {% if section.settings.button_subtext != blank %}
            <div class="why-upstep-section__button-subtext">{{ section.settings.button_subtext }}</div>
          {% endif %}
        </div>
      {% endif %}
    </div>

    <!-- Benefits Grid -->
    <div class="why-upstep-section__benefits">
      {% for block in section.blocks %}
        {% if block.type == 'benefit' %}
          <div class="why-upstep-section__benefit" {{ block.shopify_attributes }}>
            {% if block.settings.icon_type == 'custom' and block.settings.custom_icon != blank %}
              <div class="why-upstep-section__icon">
                <img src="{{ block.settings.custom_icon | image_url: width: section.settings.icon_size | default: 60 }}" alt="{{ block.settings.title }}" width="{{ section.settings.icon_size | default: 60 }}" height="{{ section.settings.icon_size | default: 60 }}" loading="lazy">
              </div>
            {% elsif block.settings.icon_type == 'preset' and block.settings.preset_icon != blank %}
              <div class="why-upstep-section__icon why-upstep-section__icon--preset">
                {% case block.settings.preset_icon %}
                  {% when 'price' %}
                    <svg width="{{ section.settings.icon_size | default: 60 }}" height="{{ section.settings.icon_size | default: 60 }}" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                      <!-- Price tag shape -->
                      <path d="M20.59 13.41l-7.17 7.17a2 2 0 0 1-2.83 0L2 12V2h10l8.59 8.59a2 2 0 0 1 0 2.82z"/>
                      <line x1="7" y1="7" x2="7.01" y2="7"/>
                      <!-- Dollar sign -->
                      <path d="M9 8h6M9 16h6M12 8V6M12 18v-2"/>
                    </svg>
                  {% when 'guarantee' %}
                    <svg width="{{ section.settings.icon_size | default: 60 }}" height="{{ section.settings.icon_size | default: 60 }}" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                      <!-- Shield shape -->
                      <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/>
                      <!-- Checkmark -->
                      <path d="M9 12l2 2 4-4"/>
                    </svg>
                  {% when 'process' %>
                    <svg width="{{ section.settings.icon_size | default: 60 }}" height="{{ section.settings.icon_size | default: 60 }}" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                      <!-- Target/crosshair -->
                      <circle cx="12" cy="12" r="10"/>
                      <circle cx="12" cy="12" r="6"/>
                      <circle cx="12" cy="12" r="2"/>
                      <!-- Cross lines -->
                      <path d="M12 2v4M12 18v4M2 12h4M18 12h4"/>
                    </svg>
                  {% when 'doctor' %>
                    <svg width="{{ section.settings.icon_size | default: 60 }}" height="{{ section.settings.icon_size | default: 60 }}" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                      <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                      <circle cx="12" cy="7" r="4"/>
                      <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                      <path d="M8 3.13a4 4 0 0 0 0 7.75"/>
                      <path d="M12 14v7"/>
                      <path d="M8 18h8"/>
                    </svg>
                  {% when 'custom_svg' %}
                    {% if block.settings.custom_svg_code != blank %}
                      {{ block.settings.custom_svg_code }}
                    {% else %}
                      <svg width="{{ section.settings.icon_size | default: 60 }}" height="{{ section.settings.icon_size | default: 60 }}" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                        <circle cx="12" cy="12" r="10"/>
                        <path d="M9 12l2 2 4-4"/>
                      </svg>
                    {% endif %}
                  {% else %}
                    <svg width="{{ section.settings.icon_size | default: 60 }}" height="{{ section.settings.icon_size | default: 60 }}" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                      <path d="M9 12l2 2 4-4"/>
                      <circle cx="12" cy="12" r="10"/>
                    </svg>
                {% endcase %}
              </div>
            {% endif %}
            
            {% if block.settings.title != blank %}
              <h3 class="why-upstep-section__benefit-title">{{ block.settings.title }}</h3>
            {% endif %}
            
            {% if block.settings.description != blank %}
              <div class="why-upstep-section__benefit-text">{{ block.settings.description }}</div>
            {% endif %}
          </div>
        {% endif %}
      {% endfor %}
    </div>
  </div>
</div>

{% stylesheet %}
  .why-upstep-section {
    width: 100%;
    max-width: none;
  }

  .why-upstep-section__container {
    display: grid;
    grid-template-columns: 1fr;
    gap: 3rem;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
  }

  /* Main Content Area */
  .why-upstep-section__main {
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
  }

  .why-upstep-section__heading {
    font-size: var(--why-upstep-heading-size);
    font-weight: var(--why-upstep-heading-weight);
    color: var(--why-upstep-heading-color);
    margin-bottom: 1rem;
    line-height: 1.2;
  }

  .why-upstep-section__description {
    font-size: var(--why-upstep-description-size);
    color: var(--why-upstep-text-color);
    margin-bottom: 1.5rem;
    line-height: 1.6;
  }

  .why-upstep-section__button-wrapper {
    margin-bottom: 1rem;
  }

  .why-upstep-section__button {
    display: inline-block;
    background: var(--why-upstep-button-bg);
    color: var(--why-upstep-button-color);
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
  }

  .why-upstep-section__button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 102, 255, 0.3);
  }

  .why-upstep-section__button-subtext {
    font-size: 0.75rem;
    color: var(--why-upstep-text-color);
    margin-top: 0.5rem;
    font-style: italic;
  }

  /* Benefits Grid */
  .why-upstep-section__benefits {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 1.5rem;
  }

  .why-upstep-section__benefit {
    text-align: center;
    padding: 1rem;
  }

  .why-upstep-section__icon {
    margin: 0 auto 1rem;
    width: var(--why-upstep-icon-size);
    height: var(--why-upstep-icon-size);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .why-upstep-section__icon--preset {
    color: #666;
  }

  .why-upstep-section__icon img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }

  .why-upstep-section__benefit-title {
    font-size: var(--why-upstep-benefit-title-size);
    font-weight: 600;
    color: var(--why-upstep-benefit-title-color);
    margin-bottom: 0.75rem;
    line-height: 1.3;
  }

  .why-upstep-section__benefit-text {
    font-size: var(--why-upstep-benefit-text-size);
    color: var(--why-upstep-benefit-text-color);
    line-height: 1.5;
  }

  /* Responsive Design */
  @media screen and (min-width: 768px) {
    .why-upstep-section__container {
      grid-template-columns: 1fr 1fr;
      gap: 4rem;
      align-items: center;
    }

    .why-upstep-section__main {
      text-align: left;
      max-width: none;
      margin: 0;
    }

    .why-upstep-section__benefits {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media screen and (max-width: 767px) {
    .why-upstep-section__benefits {
      grid-template-columns: 1fr;
      gap: 1.25rem;
    }

    .why-upstep-section__benefit {
      padding: 0.75rem;
    }

    .why-upstep-section__icon {
      margin-bottom: 0.75rem;
    }
  }
{% endstylesheet %}

{% schema %}
{
  "name": "Why Upstep?",
  "tag": "section",
  "class": "why-upstep-wrapper section-wrapper",
  "settings": [
    {
      "type": "header",
      "content": "Content"
    },
    {
      "type": "text",
      "id": "heading",
      "label": "Heading",
      "default": "Why Upstep?"
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "Description",
      "default": "<p>We will use top-quality materials for your needs and produce your custom orthotics with the most advanced manufacturing methods!</p><p>Then we will send your custom orthotics via FedEx to your doorstep.</p>"
    },
    {
      "type": "header",
      "content": "Button"
    },
    {
      "type": "text",
      "id": "button_text",
      "label": "Button text",
      "default": "TAKE THE QUIZ"
    },
    {
      "type": "url",
      "id": "button_url",
      "label": "Button URL"
    },
    {
      "type": "checkbox",
      "id": "button_open_new_tab",
      "label": "Open in new tab",
      "default": false
    },
    {
      "type": "text",
      "id": "button_subtext",
      "label": "Button subtext",
      "default": "180-day money-back guarantee"
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "heading_color",
      "label": "Heading color"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color"
    },
    {
      "type": "color",
      "id": "button_background",
      "label": "Button background",
      "default": "#0066ff"
    },
    {
      "type": "color",
      "id": "button_color",
      "label": "Button text color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "benefit_title_color",
      "label": "Benefit title color"
    },
    {
      "type": "color",
      "id": "benefit_text_color",
      "label": "Benefit text color"
    },
    {
      "type": "header",
      "content": "Typography"
    },
    {
      "type": "range",
      "id": "heading_size",
      "label": "Heading font size",
      "min": 20,
      "max": 60,
      "step": 2,
      "unit": "px",
      "default": 32
    },
    {
      "type": "range",
      "id": "heading_weight",
      "label": "Heading font weight",
      "min": 300,
      "max": 900,
      "step": 100,
      "default": 600
    },
    {
      "type": "range",
      "id": "description_size",
      "label": "Description font size",
      "min": 12,
      "max": 24,
      "step": 1,
      "unit": "px",
      "default": 16
    },
    {
      "type": "range",
      "id": "benefit_title_size",
      "label": "Benefit title font size",
      "min": 14,
      "max": 28,
      "step": 1,
      "unit": "px",
      "default": 18
    },
    {
      "type": "range",
      "id": "benefit_text_size",
      "label": "Benefit text font size",
      "min": 12,
      "max": 20,
      "step": 1,
      "unit": "px",
      "default": 14
    },
    {
      "type": "header",
      "content": "Icons"
    },
    {
      "type": "range",
      "id": "icon_size",
      "label": "Icon size",
      "min": 30,
      "max": 120,
      "step": 5,
      "unit": "px",
      "default": 60
    },
    {
      "type": "header",
      "content": "Section"
    },
    {
      "type": "select",
      "id": "section_width",
      "label": "Section width",
      "options": [
        {
          "value": "narrow",
          "label": "Narrow"
        },
        {
          "value": "normal",
          "label": "Normal"
        },
        {
          "value": "wide",
          "label": "Wide"
        },
        {
          "value": "full-width",
          "label": "Full width"
        }
      ],
      "default": "full-width"
    },
    {
      "type": "range",
      "id": "padding_top",
      "label": "Top padding",
      "min": 0,
      "max": 120,
      "step": 4,
      "unit": "px",
      "default": 60
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "label": "Bottom padding",
      "min": 0,
      "max": 120,
      "step": 4,
      "unit": "px",
      "default": 60
    }
  ],
  "blocks": [
    {
      "type": "benefit",
      "name": "Benefit",
      "settings": [
        {
          "type": "select",
          "id": "icon_type",
          "label": "Icon type",
          "options": [
            {
              "value": "preset",
              "label": "Preset icon"
            },
            {
              "value": "custom",
              "label": "Custom image"
            }
          ],
          "default": "preset"
        },
        {
          "type": "select",
          "id": "preset_icon",
          "label": "Preset icon",
          "options": [
            {
              "value": "price",
              "label": "Price/Affordable"
            },
            {
              "value": "guarantee",
              "label": "Guarantee/Shield"
            },
            {
              "value": "process",
              "label": "Process/Quick"
            },
            {
              "value": "doctor",
              "label": "Doctor/Professional"
            },
            {
              "value": "custom_svg",
              "label": "Custom SVG Code"
            }
          ],
          "default": "price"
        },
        {
          "type": "image_picker",
          "id": "custom_icon",
          "label": "Custom icon image"
        },
        {
          "type": "textarea",
          "id": "custom_svg_code",
          "label": "Custom SVG code",
          "info": "Paste your custom SVG code here. Only works when 'Custom SVG Code' is selected as preset icon."
        },
        {
          "type": "text",
          "id": "title",
          "label": "Title",
          "default": "Affordable price"
        },
        {
          "type": "textarea",
          "id": "description",
          "label": "Description",
          "default": "Upstep prices are up to 60% less than any other foot specialist."
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Why Upstep?",
      "blocks": [
        {
          "type": "benefit",
          "settings": {
            "icon_type": "preset",
            "preset_icon": "price",
            "custom_svg_code": "",
            "title": "Affordable price",
            "description": "Upstep prices are up to 60% less than any other foot specialist."
          }
        },
        {
          "type": "benefit",
          "settings": {
            "icon_type": "preset",
            "preset_icon": "guarantee",
            "custom_svg_code": "",
            "title": "Satisfaction guarantee",
            "description": "A 180 day money back guarantee and remakes with NO catches."
          }
        },
        {
          "type": "benefit",
          "settings": {
            "icon_type": "preset",
            "preset_icon": "process",
            "custom_svg_code": "",
            "title": "Quick Process",
            "description": "100% from home, start to finish the process couldn't be simpler."
          }
        },
        {
          "type": "benefit",
          "settings": {
            "icon_type": "preset",
            "preset_icon": "doctor",
            "custom_svg_code": "",
            "title": "Podiatrist designed",
            "description": "Years of podiatric knowledge and experience go into every orthotics pair."
          }
        }
      ]
    }
  ]
}
{% endschema %}

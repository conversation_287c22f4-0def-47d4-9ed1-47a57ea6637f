{% comment %}
  FAQ Orthotics Section - Frequently Asked Questions about Orthotics & Custom Insoles
  Features smooth accordion animation and mobile responsiveness
{% endcomment %}

<div class="section-background color-{{ section.settings.color_scheme }}"></div>
<div
  class="
    faq-orthotics-section spacing-style section section--{{ section.settings.section_width }} color-{{ section.settings.color_scheme }}
  "
  style="
    {% render 'spacing-style', settings: section.settings %}
    --faq-bg-color: {{ section.settings.background_color | default: 'rgba(var(--color-background), 1)' }};
    --faq-heading-color: {{ section.settings.heading_color | default: 'rgba(var(--color-foreground), 1)' }};
    --faq-text-color: {{ section.settings.text_color | default: 'rgba(var(--color-foreground), 0.75)' }};
    --faq-card-bg: {{ section.settings.card_background_color | default: 'rgba(var(--color-background), 1)' }};
    --faq-card-border: {{ section.settings.card_border_color | default: 'rgba(var(--color-border), 0.08)' }};
    --faq-question-color: {{ section.settings.question_color | default: 'rgba(var(--color-foreground), 1)' }};
    --faq-answer-color: {{ section.settings.answer_color | default: 'rgba(var(--color-foreground), 0.75)' }};
    --faq-icon-color: {{ section.settings.icon_color | default: 'rgba(var(--color-foreground), 0.6)' }};
    background: var(--faq-bg-color);
  "
>
  {% if section.settings.heading != blank %}
    <div class="faq-orthotics-section__header">
      <h2 class="faq-orthotics-section__heading">{{ section.settings.heading }}</h2>
      {% if section.settings.description != blank %}
        <div class="faq-orthotics-section__description">{{ section.settings.description }}</div>
      {% endif %}
    </div>
  {% endif %}

  {% if section.settings.category_heading != blank %}
    <div class="faq-orthotics-section__category">
      <h3 class="faq-orthotics-section__category-heading">{{ section.settings.category_heading }}</h3>
    </div>
  {% endif %}

  <div class="faq-orthotics-section__container">
    {% for block in section.blocks %}
      {% if block.type == 'faq_item' %}
        <div class="faq-orthotics-section__item" {{ block.shopify_attributes }}>
          <details class="faq-orthotics-section__details" {% if block.settings.open_by_default %}open{% endif %}>
            <summary class="faq-orthotics-section__summary">
              <span class="faq-orthotics-section__question">{{ block.settings.question }}</span>
              <span class="faq-orthotics-section__icon">
                <svg width="12" height="8" viewBox="0 0 12 8" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M1 1L6 6L11 1" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
              </span>
            </summary>
            <div class="faq-orthotics-section__content">
              <div class="faq-orthotics-section__answer">{{ block.settings.answer }}</div>
            </div>
          </details>
        </div>
      {% endif %}
    {% endfor %}
  </div>


</div>

{% stylesheet %}
  .faq-orthotics-section {
    text-align: center;
  }

  .faq-orthotics-section__header {
    margin-bottom: 3rem;
  }

  .faq-orthotics-section__heading {
    font-size: 2.5rem;
    font-weight: 600;
    color: var(--faq-heading-color);
    margin-bottom: 1rem;
    line-height: 1.2;
  }

  .faq-orthotics-section__description {
    font-size: 1.125rem;
    color: var(--faq-text-color);
    max-width: 600px;
    margin: 0 auto;
  }

  .faq-orthotics-section__category {
    margin-bottom: 2rem;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
  }

  .faq-orthotics-section__category-heading {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--faq-text-color);
    text-align: left;
    margin-bottom: 1rem;
  }

  .faq-orthotics-section__container {
    max-width: 800px;
    margin: 0 auto;
    text-align: left;
  }

  .faq-orthotics-section__item {
    margin-bottom: 1rem;
  }

  .faq-orthotics-section__details {
    background: var(--faq-card-bg);
    border: 1px solid var(--faq-card-border);
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
  }

  .faq-orthotics-section__details:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .faq-orthotics-section__summary {
    padding: 1.5rem;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    list-style: none;
    user-select: none;
    transition: all 0.3s ease;
  }

  .faq-orthotics-section__summary::-webkit-details-marker {
    display: none;
  }

  .faq-orthotics-section__summary:hover {
    background: rgba(0, 0, 0, 0.02);
  }

  .faq-orthotics-section__question {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--faq-question-color);
    line-height: 1.4;
    flex: 1;
    margin-right: 1rem;
  }

  .faq-orthotics-section__icon {
    color: var(--faq-icon-color);
    transition: transform 0.3s ease;
    flex-shrink: 0;
  }

  .faq-orthotics-section__details[open] .faq-orthotics-section__icon {
    transform: rotate(180deg);
  }

  .faq-orthotics-section__content {
    display: grid;
    grid-template-rows: 0fr;
    overflow: hidden;
    transition: grid-template-rows 0.3s ease-out, padding-bottom 0.3s ease-out;
    padding: 0 1.5rem 0;
  }

  .faq-orthotics-section__details[open] .faq-orthotics-section__content {
    grid-template-rows: 1fr;
    padding-bottom: 1.5rem;
  }

  .faq-orthotics-section__answer {
    font-size: 1rem;
    color: var(--faq-answer-color);
    line-height: 1.6;
    min-height: 0;
  }



  /* Mobile responsiveness */
  @media screen and (max-width: 768px) {
    .faq-orthotics-section__heading {
      font-size: 2rem;
      padding: 0 1rem;
    }

    .faq-orthotics-section__category {
      padding: 0 1rem;
    }

    .faq-orthotics-section__container {
      padding: 0 1rem;
    }

    .faq-orthotics-section__summary {
      padding: 1.25rem 1rem;
    }

    .faq-orthotics-section__question {
      font-size: 1rem;
    }

    .faq-orthotics-section__content {
      padding: 0 1rem 0;
    }

    .faq-orthotics-section__details[open] .faq-orthotics-section__content {
      padding-bottom: 1.25rem;
    }
  }

  @media screen and (max-width: 480px) {
    .faq-orthotics-section__heading {
      font-size: 1.75rem;
    }

    .faq-orthotics-section__category {
      padding: 0 0.75rem;
    }

    .faq-orthotics-section__container {
      padding: 0 0.75rem;
    }

    .faq-orthotics-section__summary {
      padding: 1rem 0.75rem;
    }

    .faq-orthotics-section__content {
      padding: 0 0.75rem 0;
    }

    .faq-orthotics-section__details[open] .faq-orthotics-section__content {
      padding-bottom: 1rem;
    }
  }
{% endstylesheet %}



{% schema %}
{
  "name": "FAQ Orthotics",
  "tag": "section",
  "class": "faq-orthotics-wrapper section-wrapper",
  "settings": [
    {
      "type": "text",
      "id": "heading",
      "label": "Main heading",
      "default": "Orthotics & Custom Insoles FAQ"
    },
    {
      "type": "textarea",
      "id": "description",
      "label": "Description"
    },
    {
      "type": "text",
      "id": "category_heading",
      "label": "Category heading",
      "default": "Most Popular Insoles"
    },

    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background color"
    },
    {
      "type": "color",
      "id": "heading_color",
      "label": "Heading color"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color"
    },
    {
      "type": "color",
      "id": "card_background_color",
      "label": "Card background color"
    },
    {
      "type": "color",
      "id": "card_border_color",
      "label": "Card border color"
    },
    {
      "type": "color",
      "id": "question_color",
      "label": "Question color"
    },
    {
      "type": "color",
      "id": "answer_color",
      "label": "Answer color"
    },
    {
      "type": "color",
      "id": "icon_color",
      "label": "Icon color"
    },
    {
      "type": "header",
      "content": "Section settings"
    },
    {
      "type": "select",
      "id": "section_width",
      "label": "Section width",
      "options": [
        {
          "value": "page-width",
          "label": "Page width"
        },
        {
          "value": "full-width",
          "label": "Full width"
        }
      ],
      "default": "page-width"
    },
    {
      "type": "select",
      "id": "color_scheme",
      "label": "Color scheme",
      "options": [
        {
          "value": "",
          "label": "Default"
        },
        {
          "value": "1",
          "label": "Scheme 1"
        },
        {
          "value": "2",
          "label": "Scheme 2"
        }
      ],
      "default": ""
    },
    {
      "type": "range",
      "id": "padding-block-start",
      "label": "Top padding",
      "min": 0,
      "max": 120,
      "step": 4,
      "unit": "px",
      "default": 48
    },
    {
      "type": "range",
      "id": "padding-block-end",
      "label": "Bottom padding",
      "min": 0,
      "max": 120,
      "step": 4,
      "unit": "px",
      "default": 48
    }
  ],
  "blocks": [
    {
      "type": "faq_item",
      "name": "FAQ Item",
      "settings": [
        {
          "type": "text",
          "id": "question",
          "label": "Question",
          "default": "Does insurance cover your insoles?"
        },
        {
          "type": "richtext",
          "id": "answer",
          "label": "Answer",
          "default": "<p>Answer to the frequently asked question goes here.</p>"
        },
        {
          "type": "checkbox",
          "id": "open_by_default",
          "label": "Open by default",
          "default": false
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "FAQ Orthotics",
      "blocks": [
        {
          "type": "faq_item",
          "settings": {
            "question": "Does insurance cover your insoles?",
            "answer": "<p>Many insurance plans cover custom orthotics when prescribed by a healthcare provider. We recommend checking with your insurance provider for specific coverage details.</p>"
          }
        },
        {
          "type": "faq_item",
          "settings": {
            "question": "Are they truly custom-made insoles?",
            "answer": "<p>Yes! Each pair is individually crafted based on your unique foot impression and assessment. No two pairs are exactly alike.</p>"
          }
        },
        {
          "type": "faq_item",
          "settings": {
            "question": "Will Upsteps help my condition?",
            "answer": "<p>Upsteps are designed to help with various foot conditions including plantar fasciitis, flat feet, high arches, and general foot pain. Our assessment helps determine if they're right for you.</p>"
          }
        },
        {
          "type": "faq_item",
          "settings": {
            "question": "Will I feel immediate relief?",
            "answer": "<p>In most cases, you will feel immediate relief. In some cases, your feet may need a while to get used to your Upsteps. Custom insoles support your arch and hold your feet in a healthier position.</p>",
            "open_by_default": true
          }
        },
        {
          "type": "faq_item",
          "settings": {
            "question": "What if I have a specific diagnosis from my podiatrist? Will Upstep still work with me?",
            "answer": "<p>Absolutely! We work with customers who have specific diagnoses from their podiatrists. Our team can accommodate special requirements and work with your healthcare provider's recommendations.</p>"
          }
        },
        {
          "type": "faq_item",
          "settings": {
            "question": "What kind of guarantees do you offer?",
            "answer": "<p>We offer a 180-day money-back guarantee. If you're not completely satisfied with your Upsteps, we'll provide a full refund or remake them at no additional cost.</p>"
          }
        },
        {
          "type": "faq_item",
          "settings": {
            "question": "Do you accept FSA/HSA debit cards?",
            "answer": "<p>Yes! All our custom orthotics are FSA/HSA eligible. You can use your FSA/HSA debit card at checkout or submit receipts for reimbursement.</p>"
          }
        },
        {
          "type": "faq_item",
          "settings": {
            "question": "How does Upstep compare to clinic-made orthotics?",
            "answer": "<p>Upstep orthotics provide the same level of customization and support as clinic-made orthotics, but at a fraction of the cost and with the convenience of ordering from home.</p>"
          }
        }
      ]
    }
  ]
}
{% endschema %}

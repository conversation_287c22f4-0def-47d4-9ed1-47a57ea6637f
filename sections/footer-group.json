/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "type": "footer",
  "name": "t:names.footer",
  "sections": {
    "footer": {
      "type": "footer",
      "blocks": {
        "group_bA6rnT": {
          "type": "group",
          "name": "t:names.group",
          "settings": {
            "link": "",
            "open_in_new_tab": false,
            "content_direction": "row",
            "vertical_on_mobile": true,
            "horizontal_alignment": "flex-start",
            "vertical_alignment": "flex-start",
            "align_baseline": false,
            "horizontal_alignment_flex_direction_column": "flex-start",
            "vertical_alignment_flex_direction_column": "center",
            "gap": 54,
            "width": "fill",
            "custom_width": 100,
            "width_mobile": "fill",
            "custom_width_mobile": 100,
            "height": "fit",
            "custom_height": 100,
            "inherit_color_scheme": true,
            "color_scheme": "",
            "background_media": "none",
            "video_position": "cover",
            "background_image_position": "cover",
            "border": "none",
            "border_width": 1,
            "border_opacity": 100,
            "border_radius": 0,
            "toggle_overlay": false,
            "overlay_color": "#00000026",
            "overlay_style": "solid",
            "gradient_direction": "to top",
            "padding-block-start": 0,
            "padding-block-end": 0,
            "padding-inline-start": 0,
            "padding-inline-end": 0
          },
          "blocks": {
            "menu_bHXMX6": {
              "type": "menu",
              "name": "t:names.menu",
              "settings": {
                "menu": "footer",
                "heading": "Shop",
                "menu_spacing": 12,
                "show_as_accordion": false,
                "accordion_icon": "caret",
                "accordion_dividers": false,
                "inherit_color_scheme": true,
                "color_scheme": "",
                "heading_preset": "h5",
                "link_preset": "paragraph",
                "padding-block-start": 0,
                "padding-block-end": 0,
                "padding-inline-start": 0,
                "padding-inline-end": 0
              },
              "blocks": {}
            },
            "menu_fkPhQz": {
              "type": "menu",
              "name": "t:names.menu",
              "settings": {
                "menu": "",
                "heading": "About us",
                "menu_spacing": 12,
                "show_as_accordion": false,
                "accordion_icon": "caret",
                "accordion_dividers": false,
                "inherit_color_scheme": true,
                "color_scheme": "",
                "heading_preset": "h5",
                "link_preset": "paragraph",
                "padding-block-start": 0,
                "padding-block-end": 0,
                "padding-inline-start": 0,
                "padding-inline-end": 0
              },
              "blocks": {}
            },
            "group_9LTGgk": {
              "type": "group",
              "name": "t:names.group",
              "settings": {
                "link": "",
                "open_in_new_tab": false,
                "content_direction": "column",
                "vertical_on_mobile": true,
                "horizontal_alignment": "flex-start",
                "vertical_alignment": "center",
                "align_baseline": false,
                "horizontal_alignment_flex_direction_column": "flex-start",
                "vertical_alignment_flex_direction_column": "flex-start",
                "gap": 12,
                "width": "custom",
                "custom_width": 100,
                "width_mobile": "custom",
                "custom_width_mobile": 100,
                "height": "fit",
                "custom_height": 100,
                "inherit_color_scheme": true,
                "color_scheme": "",
                "background_media": "none",
                "video_position": "cover",
                "background_image_position": "cover",
                "border": "none",
                "border_width": 1,
                "border_opacity": 100,
                "border_radius": 0,
                "toggle_overlay": false,
                "overlay_color": "#00000026",
                "overlay_style": "solid",
                "gradient_direction": "to top",
                "padding-block-start": 0,
                "padding-block-end": 0,
                "padding-inline-start": 0,
                "padding-inline-end": 0
              },
              "blocks": {
                "text_acMDpf": {
                  "type": "text",
                  "name": "t:names.text",
                  "settings": {
                    "text": "<p>Get your seat at the table</p>",
                    "width": "fit-content",
                    "max_width": "normal",
                    "alignment": "left",
                    "type_preset": "h5",
                    "font": "var(--font-tertiary--family)",
                    "font_size": "var(--font-size--body-lg)",
                    "line_height": "normal",
                    "letter_spacing": "normal",
                    "case": "none",
                    "wrap": "pretty",
                    "color": "",
                    "background": false,
                    "background_color": "#00000026",
                    "corner_radius": 0,
                    "padding-block-start": 0,
                    "padding-block-end": 0,
                    "padding-inline-start": 0,
                    "padding-inline-end": 0
                  },
                  "blocks": {}
                },
                "email_signup_dfCYKL": {
                  "type": "email-signup",
                  "name": "t:names.email_signup",
                  "settings": {
                    "width": "fill",
                    "custom_width": 100,
                    "inherit_color_scheme": false,
                    "color_scheme": "scheme-1",
                    "border_style": "all",
                    "border_width": 2,
                    "border_radius": 0,
                    "input_type_preset": "h5",
                    "style_class": "button",
                    "display_type": "text",
                    "label": "SUBSCRIBE",
                    "integrated_button": false,
                    "button_type_preset": "h5",
                    "padding-block-start": 0,
                    "padding-block-end": 0,
                    "padding-inline-start": 0,
                    "padding-inline-end": 0
                  },
                  "blocks": {}
                }
              },
              "block_order": [
                "text_acMDpf",
                "email_signup_dfCYKL"
              ]
            }
          },
          "block_order": [
            "menu_bHXMX6",
            "menu_fkPhQz",
            "group_9LTGgk"
          ]
        },
        "footer_utilities_zVWazC": {
          "type": "footer-utilities",
          "name": "t:names.footer_utilities",
          "settings": {
            "divider_thickness": 1,
            "padding-block-start": 12,
            "padding-block-end": 0
          },
          "blocks": {
            "copyright": {
              "type": "_footer-copyright",
              "static": true,
              "settings": {
                "font_size": "0.75rem",
                "case": "none"
              },
              "blocks": {}
            },
            "policy_list": {
              "type": "_footer-policy-list",
              "static": true,
              "settings": {
                "font_size": "0.75rem",
                "case": "none"
              },
              "blocks": {}
            },
            "social_icons": {
              "type": "_footer-social-icons",
              "static": true,
              "settings": {},
              "blocks": {
                "social_link_gB9Jj7": {
                  "type": "_social-link",
                  "name": "Facebook",
                  "settings": {
                    "link": "http://facebook.com/shopify"
                  },
                  "blocks": {}
                },
                "social_link_MkYhVa": {
                  "type": "_social-link",
                  "name": "Instagram",
                  "settings": {
                    "link": "http://instagram.com/shopify"
                  },
                  "blocks": {}
                },
                "social_link_DFWGmQ": {
                  "type": "_social-link",
                  "name": "TikTok",
                  "settings": {
                    "link": "https://tiktok.com/@shopify"
                  },
                  "blocks": {}
                },
                "social_link_9eWHLy": {
                  "type": "_social-link",
                  "name": "X / Twitter",
                  "settings": {
                    "link": "https://x.com/shopify"
                  },
                  "blocks": {}
                },
                "social_link_r9Bcfb": {
                  "type": "_social-link",
                  "name": "Youtube",
                  "settings": {
                    "link": "https://www.youtube.com/shopify"
                  },
                  "blocks": {}
                }
              },
              "block_order": [
                "social_link_gB9Jj7",
                "social_link_MkYhVa",
                "social_link_DFWGmQ",
                "social_link_9eWHLy",
                "social_link_r9Bcfb"
              ]
            }
          },
          "block_order": []
        }
      },
      "block_order": [
        "group_bA6rnT",
        "footer_utilities_zVWazC"
      ],
      "name": "t:names.footer",
      "settings": {
        "content_direction": "column",
        "vertical_on_mobile": true,
        "horizontal_alignment": "center",
        "vertical_alignment": "center",
        "align_baseline": false,
        "horizontal_alignment_flex_direction_column": "center",
        "vertical_alignment_flex_direction_column": "flex-start",
        "gap": 64,
        "section_width": "page-width",
        "section_height": "",
        "color_scheme": "scheme-1",
        "background_media": "none",
        "video_position": "cover",
        "background_image_position": "cover",
        "padding-block-start": 32,
        "padding-block-end": 16
      }
    }
  },
  "order": [
    "footer"
  ]
}

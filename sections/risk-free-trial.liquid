{% comment %}
  Risk-Free Trial Section - Image with content section
  Features customizable image, heading, description, button and guarantee text
{% endcomment %}

<div
  class="risk-free-trial-section"
  style="
    --risk-free-bg-color: {{ section.settings.background_color | default: '#ffffff' }};
    --risk-free-heading-color: {{ section.settings.heading_color | default: 'rgba(var(--color-foreground), 1)' }};
    --risk-free-text-color: {{ section.settings.text_color | default: 'rgba(var(--color-foreground), 0.75)' }};
    --risk-free-button-bg: {{ section.settings.button_background | default: '#0066ff' }};
    --risk-free-button-color: {{ section.settings.button_color | default: '#ffffff' }};
    --risk-free-guarantee-color: {{ section.settings.guarantee_text_color | default: 'rgba(var(--color-foreground), 0.6)' }};
    --risk-free-heading-size: {{ section.settings.heading_size | default: 36 }}px;
    --risk-free-text-size: {{ section.settings.text_size | default: 16 }}px;
    --risk-free-button-size: {{ section.settings.button_text_size | default: 16 }}px;
    --risk-free-guarantee-size: {{ section.settings.guarantee_text_size | default: 14 }}px;
    --risk-free-heading-weight: {{ section.settings.heading_weight | default: 600 }};
    background: var(--risk-free-bg-color);
    width: 100vw;
    margin-left: calc(-50vw + 50%);
  "
>
  <div class="risk-free-trial-section__container">
    <!-- Image Section -->
    <div class="risk-free-trial-section__image">
      {% if section.settings.image != blank %}
        <img 
          src="{{ section.settings.image | image_url: width: 600 }}" 
          alt="{{ section.settings.image.alt | default: section.settings.heading }}"
          width="600"
          height="auto"
          loading="lazy"
        >
      {% else %}
        <!-- Placeholder when no image is set -->
        <div class="risk-free-trial-section__placeholder">
          <div class="risk-free-trial-section__placeholder-content">
            <svg width="120" height="120" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1">
              <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
              <circle cx="8.5" cy="8.5" r="1.5"/>
              <polyline points="21,15 16,10 5,21"/>
            </svg>
            <p>Add your image in the section settings</p>
          </div>
        </div>
      {% endif %}
    </div>

    <!-- Content Section -->
    <div class="risk-free-trial-section__content">
      {% if section.settings.heading != blank %}
        <h2 class="risk-free-trial-section__heading">{{ section.settings.heading }}</h2>
      {% endif %}
      
      {% if section.settings.description != blank %}
        <div class="risk-free-trial-section__description">{{ section.settings.description }}</div>
      {% endif %}
      
      {% if section.settings.additional_text != blank %}
        <div class="risk-free-trial-section__additional-text">{{ section.settings.additional_text }}</div>
      {% endif %}
      
      {% if section.settings.button_text != blank %}
        <div class="risk-free-trial-section__button-wrapper">
          <a 
            href="{{ section.settings.button_url | default: '#' }}" 
            class="risk-free-trial-section__button"
            {% if section.settings.button_open_new_tab %}target="_blank"{% endif %}
          >
            {{ section.settings.button_text }}
          </a>
          {% if section.settings.guarantee_text != blank %}
            <div class="risk-free-trial-section__guarantee">{{ section.settings.guarantee_text }}</div>
          {% endif %}
        </div>
      {% endif %}
    </div>
  </div>
</div>

<style>
  .risk-free-trial-section {
    padding: {{ section.settings.padding_top | default: 60 }}px 0 {{ section.settings.padding_bottom | default: 60 }}px 0;
    width: 100vw;
    margin-left: calc(-50vw + 50%);
    position: relative;
  }

  .risk-free-trial-section__container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
  }

  .risk-free-trial-section__image {
    position: relative;
  }

  .risk-free-trial-section__image img {
    width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  }

  .risk-free-trial-section__placeholder {
    width: 100%;
    min-height: 300px;
    background: #f5f5f5;
    border: 2px dashed #ccc;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .risk-free-trial-section__placeholder-content {
    text-align: center;
    color: #999;
  }

  .risk-free-trial-section__placeholder svg {
    margin-bottom: 16px;
    opacity: 0.5;
  }

  .risk-free-trial-section__content {
    padding: 20px 0;
  }

  .risk-free-trial-section__heading {
    font-size: var(--risk-free-heading-size);
    font-weight: var(--risk-free-heading-weight);
    color: var(--risk-free-heading-color);
    line-height: 1.2;
    margin: 0 0 24px 0;
  }

  .risk-free-trial-section__description {
    font-size: var(--risk-free-text-size);
    color: var(--risk-free-text-color);
    line-height: 1.6;
    margin-bottom: 20px;
  }

  .risk-free-trial-section__additional-text {
    font-size: var(--risk-free-text-size);
    color: var(--risk-free-text-color);
    line-height: 1.6;
    margin-bottom: 32px;
  }

  .risk-free-trial-section__button-wrapper {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .risk-free-trial-section__button {
    display: inline-block;
    background: var(--risk-free-button-bg);
    color: var(--risk-free-button-color);
    padding: 16px 32px;
    border-radius: 6px;
    text-decoration: none;
    font-size: var(--risk-free-button-size);
    font-weight: 600;
    text-align: center;
    transition: all 0.3s ease;
    align-self: flex-start;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .risk-free-trial-section__button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    opacity: 0.9;
  }

  .risk-free-trial-section__guarantee {
    font-size: var(--risk-free-guarantee-size);
    color: var(--risk-free-guarantee-color);
    font-style: italic;
  }

  /* Mobile Responsive */
  @media screen and (max-width: 768px) {
    .risk-free-trial-section {
      padding: 40px 0;
    }

    .risk-free-trial-section__container {
      padding: 0 16px;
      grid-template-columns: 1fr;
      gap: 40px;
      text-align: center;
    }

    .risk-free-trial-section__heading {
      font-size: calc(var(--risk-free-heading-size) * 0.8);
    }

    .risk-free-trial-section__description,
    .risk-free-trial-section__additional-text {
      font-size: calc(var(--risk-free-text-size) * 0.9);
    }

    .risk-free-trial-section__button {
      align-self: center;
      width: 100%;
      max-width: 280px;
    }

    .risk-free-trial-section__placeholder {
      min-height: 200px;
    }
  }

  @media screen and (max-width: 480px) {
    .risk-free-trial-section {
      padding: 32px 0;
    }

    .risk-free-trial-section__container {
      padding: 0 12px;
      gap: 32px;
    }

    .risk-free-trial-section__heading {
      font-size: calc(var(--risk-free-heading-size) * 0.7);
      margin-bottom: 20px;
    }

    .risk-free-trial-section__description,
    .risk-free-trial-section__additional-text {
      font-size: calc(var(--risk-free-text-size) * 0.85);
      margin-bottom: 16px;
    }

    .risk-free-trial-section__button {
      padding: 14px 24px;
      font-size: calc(var(--risk-free-button-size) * 0.9);
    }
  }
</style>

{% schema %}
{
  "name": "Risk-Free Trial",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "header",
      "content": "Content Settings"
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "Section image"
    },
    {
      "type": "text",
      "id": "heading",
      "label": "Heading",
      "default": "Try your Upsteps for 180 days, Risk-Free!"
    },
    {
      "type": "textarea",
      "id": "description",
      "label": "Description",
      "default": "We 100% guarantee that you'll benefit from our custom orthotics, and if your custom orthotics don't feel right, we'll remake them until they fit perfectly, and if you're not in love with them within 180 days, we'll refund your money - no questions asked!"
    },
    {
      "type": "textarea",
      "id": "additional_text",
      "label": "Additional text",
      "default": "That's right. Just email or call us and we will provide a refund with no questions asked."
    },
    {
      "type": "text",
      "id": "button_text",
      "label": "Button text",
      "default": "TAKE THE QUIZ"
    },
    {
      "type": "url",
      "id": "button_url",
      "label": "Button URL"
    },
    {
      "type": "checkbox",
      "id": "button_open_new_tab",
      "label": "Open button link in new tab",
      "default": false
    },
    {
      "type": "text",
      "id": "guarantee_text",
      "label": "Guarantee text",
      "default": "180-day money-back guarantee"
    },
    {
      "type": "header",
      "content": "Typography Settings"
    },
    {
      "type": "range",
      "id": "heading_size",
      "min": 20,
      "max": 60,
      "step": 2,
      "unit": "px",
      "label": "Heading font size",
      "default": 36
    },
    {
      "type": "range",
      "id": "heading_weight",
      "min": 300,
      "max": 900,
      "step": 100,
      "label": "Heading font weight",
      "default": 600
    },
    {
      "type": "range",
      "id": "text_size",
      "min": 12,
      "max": 24,
      "step": 1,
      "unit": "px",
      "label": "Text font size",
      "default": 16
    },
    {
      "type": "range",
      "id": "button_text_size",
      "min": 12,
      "max": 20,
      "step": 1,
      "unit": "px",
      "label": "Button font size",
      "default": 16
    },
    {
      "type": "range",
      "id": "guarantee_text_size",
      "min": 10,
      "max": 18,
      "step": 1,
      "unit": "px",
      "label": "Guarantee text font size",
      "default": 14
    },
    {
      "type": "header",
      "content": "Color Settings"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "heading_color",
      "label": "Heading color",
      "default": "#333333"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color",
      "default": "#666666"
    },
    {
      "type": "color",
      "id": "button_background",
      "label": "Button background color",
      "default": "#0066ff"
    },
    {
      "type": "color",
      "id": "button_color",
      "label": "Button text color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "guarantee_text_color",
      "label": "Guarantee text color",
      "default": "#999999"
    },
    {
      "type": "header",
      "content": "Spacing Settings"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 120,
      "step": 4,
      "unit": "px",
      "label": "Top padding",
      "default": 60
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 120,
      "step": 4,
      "unit": "px",
      "label": "Bottom padding",
      "default": 60
    },
    {
      "type": "header",
      "content": "Section Settings"
    },
    {
      "type": "select",
      "id": "color_scheme",
      "options": [
        {
          "value": "background-1",
          "label": "Background 1"
        },
        {
          "value": "background-2",
          "label": "Background 2"
        },
        {
          "value": "inverse",
          "label": "Inverse"
        },
        {
          "value": "accent-1",
          "label": "Accent 1"
        },
        {
          "value": "accent-2",
          "label": "Accent 2"
        }
      ],
      "default": "background-1",
      "label": "Color scheme"
    }
  ],
  "presets": [
    {
      "name": "Risk-Free Trial",
      "settings": {
        "heading": "Try your Upsteps for 180 days, Risk-Free!",
        "description": "We 100% guarantee that you'll benefit from our custom orthotics, and if your custom orthotics don't feel right, we'll remake them until they fit perfectly, and if you're not in love with them within 180 days, we'll refund your money - no questions asked!",
        "additional_text": "That's right. Just email or call us and we will provide a refund with no questions asked.",
        "button_text": "TAKE THE QUIZ",
        "guarantee_text": "180-day money-back guarantee"
      }
    }
  ]
}
{% endschema %}

{% comment %}
  How It Works Section - Video section with heading
  Designed to show product demonstration video
{% endcomment %}

<div
  class="how-it-works section"
  style="
    --section-background: {{ section.settings.background_color | default: '#f8f9fa' }};
    --section-padding-top: {{ section.settings.padding_top | default: 60 }}px;
    --section-padding-bottom: {{ section.settings.padding_bottom | default: 60 }}px;
    --heading-color: {{ section.settings.heading_color | default: 'rgba(var(--color-foreground), 1)' }};
    --heading-size: {{ section.settings.heading_size | default: 32 }}px;
    --heading-weight: {{ section.settings.heading_weight | default: 600 }};
    --heading-margin-bottom: {{ section.settings.heading_margin_bottom | default: 40 }}px;
    --video-border-radius: {{ section.settings.video_border_radius | default: 12 }}px;
    --video-max-width: {{ section.settings.video_max_width | default: 800 }}px;
    --video-shadow: {{ section.settings.video_shadow | default: '0 8px 32px rgba(0, 0, 0, 0.12)' }};
    background: var(--section-background);
    padding-top: var(--section-padding-top);
    padding-bottom: var(--section-padding-bottom);
  "
  data-section-id="{{ section.id }}"
>
  <div class="how-it-works__container page-width">
    {% if section.settings.heading != blank %}
      <h2 class="how-it-works__heading">{{ section.settings.heading }}</h2>
    {% endif %}

    <div class="how-it-works__video-wrapper">
      {% if section.settings.video_type == 'youtube' and section.settings.youtube_id != blank %}
        <!-- YouTube Video -->
        <div class="how-it-works__video how-it-works__video--youtube">
          <iframe
            src="https://www.youtube.com/embed/{{ section.settings.youtube_id }}?rel=0&showinfo=0&modestbranding=1{% if section.settings.autoplay %}&autoplay=1&mute=1{% endif %}"
            frameborder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowfullscreen
            loading="lazy"
          ></iframe>
        </div>

      {% elsif section.settings.video_type == 'vimeo' and section.settings.vimeo_id != blank %}
        <!-- Vimeo Video -->
        <div class="how-it-works__video how-it-works__video--vimeo">
          <iframe
            src="https://player.vimeo.com/video/{{ section.settings.vimeo_id }}?title=0&byline=0&portrait=0{% if section.settings.autoplay %}&autoplay=1&muted=1{% endif %}"
            frameborder="0"
            allow="autoplay; fullscreen; picture-in-picture"
            allowfullscreen
            loading="lazy"
          ></iframe>
        </div>

      {% elsif section.settings.video_type == 'shopify' and section.settings.video_shopify != blank %}
        <!-- Shopify Hosted Video -->
        <div class="how-it-works__video how-it-works__video--shopify">
          <video
            {% if section.settings.autoplay %}autoplay muted{% endif %}
            {% if section.settings.loop %}loop{% endif %}
            controls
            playsinline
            preload="metadata"
          >
            {% assign video_url = section.settings.video_shopify %}
            <source src="{{ video_url }}" type="video/mp4">
            Your browser does not support the video tag.
          </video>
        </div>

      {% elsif section.settings.video_type == 'html5' %}
        <!-- HTML5 Video with multiple sources -->
        <div class="how-it-works__video how-it-works__video--html5">
          <video
            {% if section.settings.autoplay %}autoplay muted{% endif %}
            {% if section.settings.loop %}loop{% endif %}
            controls
            playsinline
            preload="metadata"
            {% if section.settings.poster_image %}poster="{{ section.settings.poster_image | image_url: width: 800 }}"{% endif %}
          >
            {% if section.settings.video_mp4 %}
              <source src="{{ section.settings.video_mp4 }}" type="video/mp4">
            {% endif %}
            {% if section.settings.video_webm %}
              <source src="{{ section.settings.video_webm }}" type="video/webm">
            {% endif %}
            Your browser does not support the video tag.
          </video>
        </div>

      {% else %}
        <!-- Placeholder when no video is set -->
        <div class="how-it-works__placeholder">
          <div class="how-it-works__placeholder-content">
            <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <polygon points="5,3 19,12 5,21"></polygon>
            </svg>
            <p>Add your video in the section settings</p>
          </div>
        </div>
      {% endif %}
    </div>

    {% if section.settings.description != blank %}
      <div class="how-it-works__description">{{ section.settings.description }}</div>
    {% endif %}
  </div>
</div>

<script>
  // Intersection Observer for lazy loading and performance
  document.addEventListener('DOMContentLoaded', function() {
    const videoSection = document.querySelector('[data-section-id="{{ section.id }}"]');
    if (!videoSection) return;

    const videoWrapper = videoSection.querySelector('.how-it-works__video');
    if (!videoWrapper) return;

    // Intersection Observer for lazy loading
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          // Load video when it comes into view
          const iframe = entry.target.querySelector('iframe');
          const video = entry.target.querySelector('video');

          if (iframe && iframe.dataset.src) {
            iframe.src = iframe.dataset.src;
            iframe.removeAttribute('data-src');
          }

          if (video && video.dataset.src) {
            video.src = video.dataset.src;
            video.removeAttribute('data-src');
            video.load();
          }

          observer.unobserve(entry.target);
        }
      });
    }, {
      rootMargin: '50px 0px',
      threshold: 0.1
    });

    observer.observe(videoWrapper);

    // Handle video autoplay on mobile with user interaction
    const video = videoWrapper.querySelector('video[autoplay]');
    if (video && /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {
      video.muted = true;
      video.setAttribute('muted', '');

      // Try to play after user interaction
      const playVideo = () => {
        video.play().catch(() => {
          // Autoplay failed, show play button
          console.log('Autoplay prevented on mobile device');
        });
        document.removeEventListener('touchstart', playVideo);
        document.removeEventListener('click', playVideo);
      };

      document.addEventListener('touchstart', playVideo, { once: true });
      document.addEventListener('click', playVideo, { once: true });
    }

    // Pause video when not in view (performance optimization)
    const pauseObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        const video = entry.target.querySelector('video');
        if (video && !video.paused) {
          if (!entry.isIntersecting) {
            video.pause();
          } else {
            video.play().catch(() => {});
          }
        }
      });
    }, {
      threshold: 0.5
    });

    pauseObserver.observe(videoWrapper);
  });
</script>

{% stylesheet %}
  .how-it-works {
    width: 100%;
  }

  .how-it-works__container {
    text-align: center;
  }

  .how-it-works__heading {
    font-size: var(--heading-size);
    font-weight: var(--heading-weight);
    color: var(--heading-color);
    margin: 0 0 var(--heading-margin-bottom) 0;
    line-height: 1.2;
  }

  .how-it-works__video-wrapper {
    max-width: var(--video-max-width);
    margin: 0 auto;
    position: relative;
  }

  .how-it-works__video {
    position: relative;
    width: 100%;
    border-radius: var(--video-border-radius);
    overflow: hidden;
    box-shadow: var(--video-shadow);
    background: #000;
  }

  /* YouTube and Vimeo responsive iframe */
  .how-it-works__video--youtube,
  .how-it-works__video--vimeo {
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
    height: 0;
  }

  .how-it-works__video--youtube iframe,
  .how-it-works__video--vimeo iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }

  /* HTML5 and Shopify video */
  .how-it-works__video--shopify video,
  .how-it-works__video--html5 video {
    width: 100%;
    height: auto;
    display: block;
  }

  /* Placeholder */
  .how-it-works__placeholder {
    aspect-ratio: 16 / 9;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f0f0f0;
    border-radius: var(--video-border-radius);
    color: #666;
  }

  .how-it-works__placeholder-content {
    text-align: center;
  }

  .how-it-works__placeholder svg {
    margin-bottom: 16px;
    opacity: 0.5;
  }

  .how-it-works__description {
    margin-top: 24px;
    font-size: 16px;
    color: rgba(var(--color-foreground), 0.8);
    line-height: 1.6;
  }

  /* Performance optimizations */
  .how-it-works__video iframe {
    will-change: transform;
  }

  .how-it-works__video video {
    object-fit: cover;
    background: #000;
  }

  /* Lazy loading support */
  .how-it-works__video[data-lazy="true"] {
    background: #f0f0f0;
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .how-it-works__video[data-lazy="true"]::before {
    content: "Loading video...";
    color: #666;
    font-size: 14px;
  }

  /* Mobile responsiveness */
  @media screen and (max-width: 768px) {
    .how-it-works {
      padding-top: var(--section-padding-top-mobile, 40px);
      padding-bottom: var(--section-padding-bottom-mobile, 40px);
    }

    .how-it-works__heading {
      font-size: var(--heading-size-mobile, 24px);
      margin-bottom: var(--heading-margin-bottom-mobile, 24px);
    }

    .how-it-works__video {
      border-radius: var(--video-border-radius-mobile, 8px);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    }

    .how-it-works__description {
      font-size: 14px;
      margin-top: 16px;
      padding: 0 16px;
    }

    .how-it-works__video-wrapper {
      margin: 0 16px;
      max-width: calc(var(--video-max-width) - 32px);
    }
  }

  @media screen and (max-width: 480px) {
    .how-it-works {
      padding-top: var(--section-padding-top-small, 32px);
      padding-bottom: var(--section-padding-bottom-small, 32px);
    }

    .how-it-works__heading {
      font-size: var(--heading-size-small, 20px);
      margin-bottom: var(--heading-margin-bottom-small, 20px);
      padding: 0 20px;
    }

    .how-it-works__video {
      border-radius: var(--video-border-radius-small, 6px);
    }

    .how-it-works__video-wrapper {
      margin: 0 20px;
      max-width: calc(100vw - 40px);
    }

    .how-it-works__description {
      padding: 0 20px;
    }
  }

  /* Touch device optimizations */
  @media (hover: none) and (pointer: coarse) {
    .how-it-works__video {
      -webkit-tap-highlight-color: transparent;
    }

    .how-it-works__video video {
      touch-action: manipulation;
    }
  }

  /* High DPI displays */
  @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .how-it-works__video {
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    }
  }

  /* Reduced motion preference */
  @media (prefers-reduced-motion: reduce) {
    .how-it-works__video video[autoplay] {
      animation: none;
    }
  }

  /* Dark mode support */
  @media (prefers-color-scheme: dark) {
    .how-it-works__placeholder {
      background: #2a2a2a;
      color: #ccc;
    }
  }
{% endstylesheet %}

{% schema %}
{
  "name": "How It Works",
  "tag": "section",
  "class": "section",
  "settings": [
    {
      "type": "header",
      "content": "Content"
    },
    {
      "type": "text",
      "id": "heading",
      "label": "Heading",
      "default": "How it works"
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "Description"
    },
    {
      "type": "header",
      "content": "Video Settings"
    },
    {
      "type": "select",
      "id": "video_type",
      "label": "Video Type",
      "options": [
        {
          "value": "youtube",
          "label": "YouTube"
        },
        {
          "value": "vimeo",
          "label": "Vimeo"
        },
        {
          "value": "shopify",
          "label": "Shopify Hosted"
        },
        {
          "value": "html5",
          "label": "HTML5 (Multiple Sources)"
        }
      ],
      "default": "youtube"
    },
    {
      "type": "text",
      "id": "youtube_id",
      "label": "YouTube Video ID",
      "info": "Example: dQw4w9WgXcQ (from https://www.youtube.com/watch?v=dQw4w9WgXcQ)"
    },
    {
      "type": "text",
      "id": "vimeo_id",
      "label": "Vimeo Video ID",
      "info": "Example: 123456789 (from https://vimeo.com/123456789)"
    },
    {
      "type": "url",
      "id": "video_shopify",
      "label": "Shopify Video URL",
      "info": "Upload video to Files and paste URL here"
    },
    {
      "type": "url",
      "id": "video_mp4",
      "label": "MP4 Video URL",
      "info": "For HTML5 video - MP4 format"
    },
    {
      "type": "url",
      "id": "video_webm",
      "label": "WebM Video URL",
      "info": "For HTML5 video - WebM format (optional)"
    },
    {
      "type": "image_picker",
      "id": "poster_image",
      "label": "Video Poster Image",
      "info": "Thumbnail shown before video plays (HTML5 only)"
    },
    {
      "type": "checkbox",
      "id": "autoplay",
      "label": "Autoplay video",
      "default": false,
      "info": "Video will be muted if autoplay is enabled"
    },
    {
      "type": "checkbox",
      "id": "loop",
      "label": "Loop video",
      "default": false,
      "info": "Only works with Shopify and HTML5 videos"
    },
    {
      "type": "header",
      "content": "Design"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background Color",
      "default": "#f8f9fa"
    },
    {
      "type": "color",
      "id": "heading_color",
      "label": "Heading Color"
    },
    {
      "type": "range",
      "id": "heading_size",
      "min": 16,
      "max": 64,
      "step": 2,
      "unit": "px",
      "label": "Heading Size",
      "default": 32
    },
    {
      "type": "range",
      "id": "heading_weight",
      "min": 300,
      "max": 900,
      "step": 100,
      "label": "Heading Weight",
      "default": 600
    },
    {
      "type": "range",
      "id": "video_max_width",
      "min": 400,
      "max": 1200,
      "step": 50,
      "unit": "px",
      "label": "Video Max Width",
      "default": 800
    },
    {
      "type": "range",
      "id": "video_border_radius",
      "min": 0,
      "max": 32,
      "step": 2,
      "unit": "px",
      "label": "Video Border Radius",
      "default": 12
    },
    {
      "type": "header",
      "content": "Spacing"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 120,
      "step": 4,
      "unit": "px",
      "label": "Top Padding",
      "default": 60
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 120,
      "step": 4,
      "unit": "px",
      "label": "Bottom Padding",
      "default": 60
    },
    {
      "type": "range",
      "id": "heading_margin_bottom",
      "min": 0,
      "max": 80,
      "step": 4,
      "unit": "px",
      "label": "Heading Bottom Margin",
      "default": 40
    }
  ],
  "presets": [
    {
      "name": "How It Works",
      "settings": {
        "heading": "How it works",
        "video_type": "youtube"
      }
    }
  ]
}
{% endschema %}

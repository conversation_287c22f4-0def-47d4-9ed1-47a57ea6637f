{% comment %}
  Product Benefits Section - Display product benefits with icons
  Designed for product pages to highlight key benefits like Upstep reference
{% endcomment %}

<div class="section-background color-{{ section.settings.color_scheme }}"></div>
<div
  class="
    product-benefits-section spacing-style section section--{{ section.settings.section_width }} color-{{ section.settings.color_scheme }}
  "
  style="
    {% render 'spacing-style', settings: section.settings %}
    --benefits-bg-color: {{ section.settings.background_color | default: 'rgba(var(--color-background), 1)' }};
    --benefits-heading-color: {{ section.settings.heading_color | default: 'rgba(var(--color-foreground), 1)' }};
    --benefits-text-color: {{ section.settings.text_color | default: 'rgba(var(--color-foreground), 0.8)' }};
    --benefits-icon-size: {{ section.settings.icon_size | default: 56 }}px;
    background: var(--benefits-bg-color);
  "
>
  <!-- FSA/HSA Block -->
  {% if section.settings.show_fsa_block %}
    <div class="product-benefits__fsa-block">
      <div class="product-benefits__fsa-content">
        <h3 class="product-benefits__fsa-title">{{ section.settings.fsa_title | default: 'Use Your FSA/HSA Dollars Before You Lose Them' }}</h3>
        {% if section.settings.fsa_link != blank %}
          <a href="{{ section.settings.fsa_link }}" class="product-benefits__fsa-link">
            <svg class="product-benefits__fsa-icon" width="16" height="16" viewBox="0 0 16 16" fill="none">
              <circle cx="8" cy="8" r="7" stroke="currentColor" stroke-width="2"/>
              <path d="M8 4v4l3 3" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
            </svg>
            {{ section.settings.fsa_link_text | default: 'Learn more' }}
          </a>
        {% endif %}
      </div>
    </div>
  {% endif %}

  <!-- About the product section -->
  {% if section.settings.heading != blank %}
    <div class="product-benefits__about">
      <h2 class="product-benefits__heading">{{ section.settings.heading }}</h2>
      {% if section.settings.description != blank %}
        <div class="product-benefits__description">{{ section.settings.description }}</div>
      {% endif %}
    </div>
  {% endif %}

  <!-- Benefits icons row -->
  <div class="product-benefits__icons-row">
    {% for block in section.blocks %}
      {% if block.type == 'benefit_item' %}
        <div class="product-benefits__benefit-item" {{ block.shopify_attributes }}>
          {% if block.settings.icon_image != blank %}
            <div class="product-benefits__benefit-icon">
              <img src="{{ block.settings.icon_image | image_url: width: 56 }}" alt="{{ block.settings.title }}" width="56" height="56" loading="lazy">
            </div>
          {% elsif block.settings.icon_emoji != blank %}
            <div class="product-benefits__benefit-icon product-benefits__benefit-icon--emoji">
              {{ block.settings.icon_emoji }}
            </div>
          {% endif %}

          {% if block.settings.title != blank %}
            <div class="product-benefits__benefit-title">{{ block.settings.title }}</div>
          {% endif %}
        </div>
      {% endif %}
    {% endfor %}
  </div>
</div>

{% stylesheet %}
  .product-benefits-section {
    text-align: left;
    max-width: 100%;
    border-top: 1px solid rgba(var(--color-foreground), 0.1);
    padding-top: 1.5rem;
  }

  /* FSA/HSA Block */
  .product-benefits__fsa-block {
    background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    color: white;
  }

  .product-benefits__fsa-content {
    text-align: center;
  }

  .product-benefits__fsa-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0 0 1rem 0;
    line-height: 1.3;
  }

  .product-benefits__fsa-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: white;
    text-decoration: none;
    font-size: 0.95rem;
    font-weight: 500;
    border-bottom: 1px solid rgba(255, 255, 255, 0.3);
    padding-bottom: 2px;
    transition: border-color 0.2s ease;
  }

  .product-benefits__fsa-link:hover {
    border-bottom-color: white;
  }

  .product-benefits__fsa-icon {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
  }

  /* About section */
  .product-benefits__about {
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid rgba(var(--color-foreground), 0.1);
  }

  .product-benefits__heading {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--benefits-heading-color);
    margin-bottom: 0.75rem;
    line-height: 1.3;
  }

  .product-benefits__description {
    font-size: 0.95rem;
    color: var(--benefits-text-color);
    line-height: 1.6;
    margin-bottom: 0;
  }

  /* Benefits icons row */
  .product-benefits__icons-row {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 1rem;
    margin-top: 0;
    padding-top: 1.5rem;
  }

  .product-benefits__benefit-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    flex: 1;
    min-width: 0;
  }

  .product-benefits__benefit-icon {
    width: 42px;
    height: 42px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 0.5rem;
    flex-shrink: 0;
  }

  .product-benefits__benefit-icon img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }

  .product-benefits__benefit-icon--emoji {
    font-size: 1.25rem;
    color: rgba(var(--color-foreground), 0.8);
  }

  .product-benefits__benefit-title {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--benefits-heading-color);
    line-height: 1.3;
    text-align: center;
  }

  /* Mobile responsiveness */
  @media screen and (max-width: 768px) {
    .product-benefits__fsa-title {
      font-size: 1.125rem;
    }

    .product-benefits__heading {
      font-size: 1rem;
    }

    .product-benefits__description {
      font-size: 0.875rem;
    }

    .product-benefits__icons-row {
      flex-wrap: wrap;
      gap: 1.5rem;
    }

    .product-benefits__benefit-item {
      flex: 1 0 30%;
      min-width: 80px;
    }
  }

  @media screen and (max-width: 480px) {
    .product-benefits__fsa-block {
      padding: 1.25rem 1rem;
    }

    .product-benefits__fsa-title {
      font-size: 1rem;
      margin-bottom: 0.75rem;
    }

    .product-benefits__benefit-icon {
      width: 40px;
      height: 40px;
    }

    .product-benefits__benefit-icon--emoji {
      font-size: 1.25rem;
    }

    .product-benefits__benefit-title {
      font-size: 0.75rem;
    }

    .product-benefits__icons-row {
      margin-top: 1.5rem;
    }
  }
{% endstylesheet %}

{% schema %}
{
  "name": "Product Benefits",
  "tag": "section",
  "class": "product-benefits-wrapper section-wrapper",
  "settings": [
    {
      "type": "header",
      "content": "FSA/HSA Block"
    },
    {
      "type": "checkbox",
      "id": "show_fsa_block",
      "label": "Show FSA/HSA block",
      "default": true
    },
    {
      "type": "text",
      "id": "fsa_title",
      "label": "FSA/HSA title",
      "default": "Use Your FSA/HSA Dollars Before You Lose Them"
    },
    {
      "type": "url",
      "id": "fsa_link",
      "label": "FSA/HSA link"
    },
    {
      "type": "text",
      "id": "fsa_link_text",
      "label": "FSA/HSA link text",
      "default": "Learn more"
    },
    {
      "type": "header",
      "content": "About Product Section"
    },
    {
      "type": "text",
      "id": "heading",
      "label": "Heading",
      "default": "About the product"
    },
    {
      "type": "textarea",
      "id": "description",
      "label": "Description",
      "default": "It's all about reducing fatigue and improving comfort! Power Walking & Hiking Upsteps smooth out the natural heel-to-toe 'roll' your feet make as you walk. They're individually designed for you from durable materials to prevent damage and reduce or eliminate pain."
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Section background color"
    },
    {
      "type": "color",
      "id": "heading_color",
      "label": "Heading color"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color"
    },
    {
      "type": "header",
      "content": "Icon settings"
    },
    {
      "type": "range",
      "id": "icon_size",
      "label": "Icon size",
      "min": 40,
      "max": 80,
      "step": 4,
      "unit": "px",
      "default": 48
    },
    {
      "type": "header",
      "content": "Section settings"
    },
    {
      "type": "select",
      "id": "section_width",
      "label": "Section width",
      "options": [
        {
          "value": "page-width",
          "label": "Page width"
        },
        {
          "value": "full-width",
          "label": "Full width"
        }
      ],
      "default": "page-width"
    },
    {
      "type": "select",
      "id": "color_scheme",
      "label": "Color scheme",
      "options": [
        {
          "value": "",
          "label": "Default"
        },
        {
          "value": "1",
          "label": "Scheme 1"
        },
        {
          "value": "2",
          "label": "Scheme 2"
        }
      ],
      "default": ""
    },
    {
      "type": "range",
      "id": "padding-block-start",
      "label": "Top padding",
      "min": 0,
      "max": 120,
      "step": 4,
      "unit": "px",
      "default": 40
    },
    {
      "type": "range",
      "id": "padding-block-end",
      "label": "Bottom padding",
      "min": 0,
      "max": 120,
      "step": 4,
      "unit": "px",
      "default": 40
    }
  ],
  "blocks": [
    {
      "type": "benefit_item",
      "name": "Benefit Item",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Title",
          "default": "180-day money-back"
        },
        {
          "type": "image_picker",
          "id": "icon_image",
          "label": "Icon image"
        },
        {
          "type": "text",
          "id": "icon_emoji",
          "label": "Icon (emoji - fallback)",
          "default": "✓",
          "info": "Used if no image is selected"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Product Benefits",
      "settings": {
        "show_fsa_block": true,
        "fsa_title": "Use Your FSA/HSA Dollars Before You Lose Them",
        "fsa_link_text": "Learn more",
        "heading": "About the product",
        "description": "It's all about reducing fatigue and improving comfort! Power Walking & Hiking Upsteps smooth out the natural heel-to-toe 'roll' your feet make as you walk. They're individually designed for you from durable materials to prevent damage and reduce or eliminate pain."
      },
      "blocks": [
        {
          "type": "benefit_item",
          "settings": {
            "title": "180-day money-back",
            "icon_emoji": "✓"
          }
        },
        {
          "type": "benefit_item",
          "settings": {
            "title": "Free shipping",
            "icon_emoji": "🚚"
          }
        },
        {
          "type": "benefit_item",
          "settings": {
            "title": "FSA/HSA Eligible",
            "icon_emoji": "💳"
          }
        }
      ]
    }
  ]
}
{% endschema %}

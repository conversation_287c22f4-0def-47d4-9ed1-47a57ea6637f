

<div class="section-background color-{{ section.settings.color_scheme }}"></div>
<aside
  class="announcement-bar spacing-style section section--{{ section.settings.section_width }} color-{{ section.settings.color_scheme }}"
  style="
    {% render 'spacing-padding', settings: section.settings %};
    --border-bottom-width: {{ section.settings.divider_width }}px;
    --announcement-bg-color: {{ section.settings.background_color | default: '#4a7c59' }};
    --announcement-text-color: {{ section.settings.text_color | default: '#ffffff' }};
    --announcement-static-text-color: {{ section.settings.static_text_color | default: '#ffffff' }};
    --static-font-size: {{ section.settings.static_font_size | default: 0.875 }}rem;
    --scrolling-font-size: {{ section.settings.scrolling_font_size | default: 0.875 }}rem;
    --static-font-weight: {{ section.settings.static_font_weight | default: 500 }};
    --scrolling-font-weight: {{ section.settings.scrolling_font_weight | default: 600 }};
    --announcement-padding: {{ section.settings.announcement_padding | default: 12 }}px;
    --static-margin-bottom: {{ section.settings.static_margin_bottom | default: 4 }}px;
    --scrolling-height: {{ section.settings.scrolling_height | default: 20 }}px;
  "
>
  <div class="announcement-bar__content">
    {% if section.settings.static_text != blank %}
      <div class="announcement-bar__static">
        {{ section.settings.static_text }}
      </div>
    {% endif %}
    <div class="announcement-bar__scrolling" id="scrolling-announcements">
      {% for block in section.blocks %}
        {% if block.type == 'announcement' %}
          <div class="announcement-bar__slide" data-index="{{ forloop.index0 }}">
            {% if block.settings.link != blank %}
              <a href="{{ block.settings.link }}" {% if block.settings.open_in_new_tab %}target="_blank"{% endif %}>
                {{ block.settings.text }}
              </a>
            {% else %}
              {{ block.settings.text }}
            {% endif %}
          </div>
        {% endif %}
      {% endfor %}
    </div>
  </div>
</aside>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const container = document.getElementById('scrolling-announcements');
  if (!container) {
    return;
  }

  const slides = container.querySelectorAll('.announcement-bar__slide');

  let currentIndex = 0;

  if (slides.length > 1) {
    // Show first slide
    slides[0].classList.add('active');

    function nextSlide() {

      // Hide current slide
      slides[currentIndex].classList.remove('active');
      slides[currentIndex].classList.add('exit');

      // Move to next slide
      currentIndex = (currentIndex + 1) % slides.length;

      // Show next slide after a short delay
      setTimeout(() => {
        slides.forEach(slide => slide.classList.remove('exit'));
        slides[currentIndex].classList.add('active');
      }, 300);
    }

    setInterval(nextSlide, 4000);
  } else if (slides.length === 1) {
    slides[0].classList.add('active');
  }
});
</script>

{% stylesheet %}
  .announcement-bar {
    border-block-end: var(--border-bottom-width) solid var(--color-border);
    background: var(--announcement-bg-color) !important;
    padding: var(--announcement-padding) 0;
  }

  .announcement-bar .color-scheme-2 {
    background: var(--announcement-bg-color) !important;
    color: var(--announcement-text-color) !important;
  }

  .announcement-bar__content {
    text-align: center;
    color: var(--announcement-text-color);
    font-weight: var(--static-font-weight);
  }

  .announcement-bar__static {
    font-size: var(--static-font-size);
    margin-bottom: var(--static-margin-bottom);
    opacity: 0.9;
    color: var(--announcement-static-text-color) !important;
    font-weight: var(--static-font-weight);
  }

  .announcement-bar__scrolling {
    font-size: var(--scrolling-font-size);
    font-weight: var(--scrolling-font-weight);
    height: var(--scrolling-height);
    overflow: hidden;
    position: relative;
  }

  .announcement-bar__slide {
    position: absolute;
    width: 100%;
    top: 0;
    left: 0;
    color: var(--announcement-text-color) !important;
    font-size: var(--scrolling-font-size);
    font-weight: var(--scrolling-font-weight);
    text-align: center;
    transition: transform 0.6s ease-in-out, opacity 0.6s ease-in-out;
    transform: translateY(20px);
    opacity: 0;
  }

  .announcement-bar__slide.active {
    transform: translateY(0);
    opacity: 1;
  }

  .announcement-bar__slide.exit {
    transform: translateY(-20px);
    opacity: 0;
  }

  .announcement-bar__slide a {
    color: var(--announcement-text-color) !important;
    text-decoration: none;
  }

  .announcement-bar__slide a:hover {
    color: var(--announcement-text-color) !important;
    opacity: 0.8;
  }



  /* Mobile responsiveness */
  @media screen and (max-width: 768px) {
    .announcement-bar__static {
      font-size: calc(var(--static-font-size) * 0.9);
      margin-bottom: calc(var(--static-margin-bottom) * 0.75);
    }

    .announcement-bar__slide {
      font-size: calc(var(--scrolling-font-size) * 0.9);
    }

    .announcement-bar__scrolling {
      height: calc(var(--scrolling-height) * 0.9);
    }

    .announcement-bar {
      padding: calc(var(--announcement-padding) * 0.8) 0;
    }
  }
{% endstylesheet %}

{% schema %}
{
  "name": "t:names.announcement_bar",
  "blocks": [
    {
      "type": "announcement",
      "name": "Announcement",
      "settings": [
        {
          "type": "text",
          "id": "text",
          "label": "Text",
          "default": "Announcement text"
        },
        {
          "type": "url",
          "id": "link",
          "label": "Link"
        },
        {
          "type": "checkbox",
          "id": "open_in_new_tab",
          "label": "Open in new tab",
          "default": false
        }
      ]
    }
  ],
  "enabled_on": {
    "groups": ["header"]
  },
  "settings": [
    {
      "type": "text",
      "id": "static_text",
      "label": "Static text (top line)",
      "default": "Bring Nature Indoors",
      "info": "Text that appears above the scrolling announcements"
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background color",
      "default": "#4a7c59"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Scrolling text color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "static_text_color",
      "label": "Static text color",
      "default": "#ffffff"
    },
    {
      "type": "header",
      "content": "Typography & Spacing"
    },
    {
      "type": "range",
      "id": "static_font_size",
      "label": "Static text font size",
      "min": 0.7,
      "max": 1.2,
      "step": 0.1,
      "unit": "rem",
      "default": 0.9
    },
    {
      "type": "range",
      "id": "scrolling_font_size",
      "label": "Scrolling text font size",
      "min": 0.7,
      "max": 1.2,
      "step": 0.1,
      "unit": "rem",
      "default": 0.9
    },
    {
      "type": "range",
      "id": "static_font_weight",
      "label": "Static text font weight",
      "min": 300,
      "max": 700,
      "step": 100,
      "default": 500
    },
    {
      "type": "range",
      "id": "scrolling_font_weight",
      "label": "Scrolling text font weight",
      "min": 300,
      "max": 700,
      "step": 100,
      "default": 600
    },
    {
      "type": "range",
      "id": "announcement_padding",
      "label": "Vertical padding",
      "min": 8,
      "max": 24,
      "step": 2,
      "unit": "px",
      "default": 12
    },
    {
      "type": "range",
      "id": "static_margin_bottom",
      "label": "Space between static and scrolling text",
      "min": 0,
      "max": 12,
      "step": 1,
      "unit": "px",
      "default": 4
    },
    {
      "type": "range",
      "id": "scrolling_height",
      "label": "Scrolling text height",
      "min": 16,
      "max": 32,
      "step": 2,
      "unit": "px",
      "default": 20
    },
    {
      "type": "header",
      "content": "Animation"
    },
    {
      "type": "range",
      "id": "speed",
      "label": "t:settings.speed",
      "min": 2,
      "max": 10,
      "default": 5,
      "unit": "sec"
    },
    {
      "type": "header",
      "content": "t:content.appearance"
    },
    {
      "type": "select",
      "id": "section_width",
      "label": "t:settings.section_width",
      "options": [
        {
          "value": "page-width",
          "label": "t:options.page"
        },
        {
          "value": "full-width",
          "label": "t:options.full"
        }
      ],
      "default": "page-width"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "default": "scheme-4",
      "label": "t:settings.color_scheme"
    },
    {
      "type": "range",
      "id": "divider_width",
      "label": "t:settings.divider_thickness",
      "min": 0,
      "max": 5,
      "step": 0.5,
      "unit": "px",
      "default": 0
    },
    {
      "type": "header",
      "content": "t:content.padding"
    },
    {
      "type": "range",
      "id": "padding-block-start",
      "label": "t:settings.top",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 15
    },
    {
      "type": "range",
      "id": "padding-block-end",
      "label": "t:settings.bottom",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 15
    }
  ],
  "presets": [
    {
      "name": "t:names.announcement_bar",
      "blocks": {
        "announcement_1": {
          "type": "announcement"
        }
      },
      "block_order": ["announcement_1"]
    }
  ]
}
{% endschema %}

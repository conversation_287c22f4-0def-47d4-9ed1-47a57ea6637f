{%- doc -%}
  Renders a wrapper section

  @param {section} section - The section object
  @param {string} children - The children of the section
{%- enddoc -%}

<div class="section-background color-{{ section.settings.color_scheme }}"></div>
<div
  class="section section--{{ section.settings.section_width }} color-{{ section.settings.color_scheme }}"

  {% if request.visual_preview_mode %}
    data-shopify-visual-preview
  {% endif %}

  style="
    {% if section.settings.section_height == 'custom' %}
      --section-min-height: {{ section.settings.section_height_custom }}svh;
    {% elsif section.settings.section_height == 'full-screen' %}
      --section-min-height: 100svh;
    {% elsif section.settings.section_height != '' %}
      --section-min-height: var(--section-height-{{ section.settings.section_height }});
    {% endif %}

    {% if section.settings.section_height != '' %}
     --section-preview-height: 600px;
     {% endif %}
  "
>
  <div class="custom-section-background">
    {% render 'background-media',
      background_media: section.settings.background_media,
      background_video: section.settings.video,
      background_video_position: section.settings.video_position,
      background_image: section.settings.background_image,
      background_image_position: section.settings.background_image_position
    %}
  </div>

  <div
    class="border-style custom-section-content"
    style="{% render 'border-override', settings: section.settings %}"
  >
    {% if section.settings.toggle_overlay %}
      {% render 'overlay', settings: section.settings, layer: '0' %}
    {% endif %}

    <div
      class="
        spacing-style
        layout-panel-flex
        layout-panel-flex--{{ section.settings.content_direction }}
        section-content-wrapper
        {% if section.settings.vertical_on_mobile %} mobile-column{% endif %}
      "
      style="
        {% render 'layout-panel-style', settings: section.settings %}
        {% render 'spacing-style', settings: section.settings %}
      "
      data-testid="section-content"
    >
      {{ children }}
    </div>
  </div>
</div>

{% stylesheet %}
  .section-wrapper {
    --section-height-offset: 0px;
  }

  .section[data-shopify-visual-preview] {
    min-height: var(--section-preview-height);
    padding-top: 0;
  }

  .section[data-shopify-visual-preview] .custom-section-background {
    display: none;
  }

  body:has(> #header-group > .header-section > #header-component[transparent]):not(
      :has(> #header-group > .header-section + .shopify-section)
    )
    > main
    > .section-wrapper:first-child {
    --section-height-offset: var(--header-group-height, 0);
  }

  .custom-section-background {
    grid-column: 1 / -1;
  }

  .custom-section-content {
    z-index: var(--layer-flat);
  }
{% endstylesheet %}

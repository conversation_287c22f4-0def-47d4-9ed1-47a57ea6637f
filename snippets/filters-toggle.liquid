{%- doc -%}
  Renders the sorting component.

  @param {boolean} enable_filtering - Whether to enable filtering
  @param {number} padding-block-start - The padding-block-start value
  @param {number} padding-block-end - The padding-block-end value
  @param {number} total_active_values - The total number of active values
  @param {string} section_id - The section ID
  @param {object} results - The results of the search
  @param {string} sort_by - The current sort by
{%- enddoc -%}

<div
  class="facets-toggle{% if block.settings.enable_filtering == false %} facets-toggle--no-filters{% endif %}"
  style="--facets-inner-padding-block: {{ padding-block-start }}px {{ padding-block-end }}px; --facets-inner-padding-inline: var(--padding-lg);"
>
  {% if enable_filtering %}
    <div class="facets-toggle__wrapper">
      <button
        class="button facets-toggle__button button-unstyled button-unstyled--with-icon"
        on:click="#filters-drawer/showDialog"
        type="button"
      >
        <span class="svg-wrapper">
          {{ 'icon-filter.svg' | inline_asset_content }}
        </span>

        {{ 'actions.show_filters' | t }}

        {% if total_active_values > 0 %}
          <div
            id="filter-count-bubble-toggle"
            class="filter-count-bubble"
            aria-label="{{ 'accessibility.filter_count' | t: count: total_active_values }}"
          >
            <span class="filter-count-bubble__background"></span>

            <span
              ref="cartBubbleText"
              class="filter-count-bubble__text"
              aria-hidden="true"
            >
              {{ total_active_values }}
            </span>
          </div>
        {% endif %}
      </button>
    </div>
  {% endif %}

  <div class="facets-mobile-wrapper facets-controls-wrapper">
    {% if block.settings.enable_filtering == false and block.settings.enable_sorting %}
      {% render 'sorting',
        results: results,
        sort_by: sort_by,
        filter_style: block.settings.filter_style,
        suffix: 'mobile',
        section_id: section_id,
        should_use_select_on_mobile: false
      %}
    {% endif %}
    {% if block.settings.enable_grid_density %}
      {% render 'grid-density-controls', viewport: 'mobile' %}
    {% endif %}
  </div>
</div>

{% stylesheet %}
  /* Facets - Toggle */
  .facets-toggle {
    --icon-offset: -3px;

    display: flex;
    justify-content: space-between;
    align-items: center;
    height: var(--minimum-touch-target);
    margin: var(--facets-margin);
    padding-block: var(--facets-inner-padding-block);
    padding-inline: var(--facets-inner-padding-inline);

    @media screen and (min-width: 750px) {
      display: none;
    }
  }

  .facets-toggle__wrapper {
    margin-left: var(--icon-offset);
  }

  .facets-toggle__button {
    box-shadow: none;

    @media screen and (min-width: 750px) {
      display: none;
    }
  }

  /* Filter count*/
  .filter-count-bubble {
    position: relative;
    width: 20px;
    aspect-ratio: 1;
    border-radius: 50%;
    display: grid;
    line-height: normal;
    place-content: center;
    color: var(--color-foreground);
    border: var(--icon-stroke-width) solid var(--color-background);
  }

  .facets-mobile__title-wrapper .h3 {
    margin-block-end: 0;
    display: inline-flex;
    align-items: center;
    gap: var(--gap-xs);
  }

  .facets-mobile__title-wrapper .filter-count-bubble {
    width: 22px;
    height: 22px;
  }

  .facets-mobile__title-wrapper .filter-count-bubble__text {
    font-size: var(--font-size--xs);
  }

  .filter-count-bubble__background {
    position: absolute;
    inset: 0;
    background-color: rgb(from var(--color-foreground) r g b / var(--opacity-10-25));
    border-radius: var(--style-border-radius-50);
  }

  .filter-count-bubble__text {
    font-size: 11px;
    font-weight: var(--font-paragraph--weight);
    aspect-ratio: 1 / 1;
  }

  .facets-toggle--no-filters {
    @media screen and (max-width: 749px) {
      justify-content: unset !important;

      & > .facets-mobile-wrapper {
        width: 100%;
      }
    }
  }
{% endstylesheet %}

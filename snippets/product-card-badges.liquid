{%- doc -%}
  Renders product badges for the product card.

  @param {object} product - The product object.
  @param {object} settings - The theme settings object.

  @example
  {% render 'product-card-badges', product: product, settings: settings %}
{%- enddoc -%}

<div
  class="product-badges product-badges--{{ settings.badge_position }}"
  style="
    --badge-border-radius: {{ settings.badge_corner_radius }}px;
    --badge-font-family: var(--font-{{ settings.badge_font_family }}--family); --badge-font-weight: var(--font-{{ settings.badge_font_family }}--weight); --badge-text-transform: {{ settings.badge_text_transform }};
  "
>
  {%- if product.available == false or product.compare_at_price > product.price and product.available -%}
    <div
      class="
        product-badges__badge product-badges__badge--rectangle
        {% if product.available == false %} color-{{ settings.badge_sold_out_color_scheme }}{% elsif product.compare_at_price > product.price %} color-{{ settings.badge_sale_color_scheme }}{% endif %}
      "
    >
      {%- if product.available == false -%}
        {{ 'content.product_badge_sold_out' | t }}
      {%- elsif product.compare_at_price > product.price -%}
        {{ 'content.product_badge_sale' | t }}
      {%- endif -%}
    </div>
  {%- endif -%}
</div>

{% stylesheet %}
  .product-badges {
    position: absolute;
    z-index: var(--layer-flat);
    --badge-inset: max(var(--padding-xs), calc((var(--border-radius) + var(--padding-xs)) * (1 - cos(45deg))));
  }

  .product-badges--bottom-left {
    bottom: calc(var(--badge-inset) + var(--padding-block-start));
    left: calc(var(--badge-inset) + var(--padding-inline-start));
  }

  .product-badges--top-left {
    top: calc(var(--badge-inset) + var(--padding-block-start));
    left: calc(var(--badge-inset) + var(--padding-inline-start));
  }

  .product-badges--top-right {
    top: calc(var(--badge-inset) + var(--padding-block-start));
    right: calc(var(--badge-inset) + var(--padding-inline-start));
  }

  .product-badges__badge {
    --badge-font-size: var(--font-size--xs);

    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: var(--color-foreground);
    background: var(--color-background);
    font-size: var(--badge-font-size);
    font-family: var(--badge-font-family);
    font-weight: var(--badge-font-weight);
    text-transform: var(--badge-text-transform);
    border-radius: var(--badge-border-radius);
  }

  .product-badges__badge--rectangle {
    padding-block: var(--badge-rectangle-padding-block);
    padding-inline: var(--badge-rectangle-padding-inline);
  }
{% endstylesheet %}

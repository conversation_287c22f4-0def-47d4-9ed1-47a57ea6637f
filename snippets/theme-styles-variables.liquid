{%- liquid
  assign primary_font_bold = settings.type_body_font | font_modify: 'weight', 'bold'
  assign primary_font_italic = settings.type_body_font | font_modify: 'style', 'italic'
  assign primary_font_bold_italic = primary_font_bold | font_modify: 'style', 'italic'

  assign secondary_font_bold = settings.type_subheading_font | font_modify: 'weight', 'bold'
  assign secondary_font_italic = settings.type_subheading_font | font_modify: 'style', 'italic'
  assign secondary_font_bold_italic = secondary_font_bold | font_modify: 'style', 'italic'

  assign tertiary_font_bold = settings.type_heading_font | font_modify: 'weight', 'bold'
  assign tertiary_font_italic = settings.type_heading_font | font_modify: 'style', 'italic'
  assign tertiary_font_bold_italic = tertiary_font_bold | font_modify: 'style', 'italic'

  assign accent_font_bold = settings.type_accent_font | font_modify: 'weight', 'bold'
  assign accent_font_italic = settings.type_accent_font | font_modify: 'style', 'italic'
  assign accent_font_bold_italic = accent_font_bold | font_modify: 'style', 'italic'
%}

{% style %}
  {{ settings.type_body_font | font_face: font_display: 'swap' }}
  {{ primary_font_bold | font_face: font_display: 'swap' }}
  {{ primary_font_italic | font_face: font_display: 'swap' }}
  {{ primary_font_bold_italic | font_face: font_display: 'swap' }}

  {{ settings.type_subheading_font | font_face: font_display: 'swap' }}
  {{ secondary_font_bold | font_face: font_display: 'swap' }}
  {{ secondary_font_italic | font_face: font_display: 'swap' }}
  {{ secondary_font_bold_italic | font_face: font_display: 'swap' }}

  {{ settings.type_heading_font | font_face: font_display: 'swap' }}
  {{ tertiary_font_bold | font_face: font_display: 'swap' }}
  {{ tertiary_font_italic | font_face: font_display: 'swap' }}
  {{ tertiary_font_bold_italic | font_face: font_display: 'swap' }}

  {{ settings.type_accent_font | font_face: font_display: 'swap' }}
  {{ accent_font_bold | font_face: font_display: 'swap' }}
  {{ accent_font_italic | font_face: font_display: 'swap' }}
  {{ accent_font_bold_italic | font_face: font_display: 'swap' }}

  :root {
    /* Page Layout */
    --sidebar-width: 25rem;
    --narrow-content-width: 36rem;
    --normal-content-width: 42rem;
    --wide-content-width: 46rem;
    --narrow-page-width: 90rem;
    --normal-page-width: 120rem;
    --wide-page-width: 150rem;

    /* Section Heights */
    --section-height-small: 15rem;
    --section-height-medium: 25rem;
    --section-height-large: 35rem;

    @media screen and (min-width: 40em) {
      --section-height-small: 40svh;
      --section-height-medium: 55svh;
      --section-height-large: 70svh;
    }

    @media screen and (min-width: 60em) {
      --section-height-small: 50svh;
      --section-height-medium: 65svh;
      --section-height-large: 80svh;
    }

    /* Letter spacing */
    --letter-spacing-sm: 0.06em;
    --letter-spacing-md: 0.13em;

    /* Font families */
    --font-body--family: {{ settings.type_body_font.family }}, {{ settings.type_body_font.fallback_families }};
    --font-body--style: {{ settings.type_body_font.style }};
    --font-body--weight: {{ settings.type_body_font.weight }};
    --font-subheading--family: {{ settings.type_subheading_font.family }}, {{ settings.type_subheading_font.fallback_families }};
    --font-subheading--style: {{ settings.type_subheading_font.style }};
    --font-subheading--weight: {{ settings.type_subheading_font.weight }};
    --font-heading--family: {{ settings.type_heading_font.family }}, {{ settings.type_heading_font.fallback_families }};
    --font-heading--style: {{ settings.type_heading_font.style }};
    --font-heading--weight: {{ settings.type_heading_font.weight }};
    --font-accent--family: {{ settings.type_accent_font.family }}, {{ settings.type_accent_font.fallback_families }};
    --font-accent--style: {{ settings.type_accent_font.style }};
    --font-accent--weight: {{ settings.type_accent_font.weight }};

    /* Margin sizes */
    --font-h1--spacing: 0.25em;
    --font-h2--spacing: 0.25em;
    --font-h3--spacing: 0.25em;
    --font-h4--spacing: 0.25em;
    --font-h5--spacing: 0.25em;
    --font-h6--spacing: 0.25em;
    --font-paragraph--spacing: 0.5em;

    /* Heading colors */
    --font-h1--color: var(--color-foreground-heading);
    --font-h2--color: var(--color-foreground-heading);
    --font-h3--color: var(--color-foreground-heading);
    --font-h4--color: var(--color-foreground-heading);
    --font-h5--color: var(--color-foreground-heading);
    --font-h6--color: var(--color-foreground-heading);

    /** Z-Index / Layering */
    --layer-section-background: -2;
    --layer-lowest: -1;
    --layer-base: 0;
    --layer-flat: 1;
    --layer-raised: 2;
    --layer-heightened: 4;
    --layer-sticky: 8;
    --layer-window-overlay: 10;
    --layer-header-menu: 12;
    --layer-overlay: 16;
    --layer-menu-drawer: 18;
    --layer-temporary: 20;

    /* Max-width / Measure */
    --max-width--body-normal: 50ch;
    --max-width--body-narrow: 35ch;

    --max-width--heading-normal: 50ch;
    --max-width--heading-narrow: 30ch;

    --max-width--display-normal: 20ch;
    --max-width--display-narrow: 15ch;
    --max-width--display-tight: 5ch;

    /* Letter-spacing / Tracking */
    --letter-spacing--display-tight: -0.03em;
    --letter-spacing--display-normal: 0;
    --letter-spacing--display-loose: 0.03em;

    --letter-spacing--heading-tight: -0.03em;
    --letter-spacing--heading-normal: 0;
    --letter-spacing--heading-loose: 0.03em;

    --letter-spacing--body-tight: -0.03em;
    --letter-spacing--body-normal: 0;
    --letter-spacing--body-loose: 0.03em;

    /* Line height / Leading */
    --line-height: 1;

    --line-height--display-tight: 1;
    --line-height--display-normal: 1.1;
    --line-height--display-loose: 1.2;

    --line-height--heading-tight: 1.15;
    --line-height--heading-normal: 1.25;
    --line-height--heading-loose: 1.35;

    --line-height--body-tight: 1.2;
    --line-height--body-normal: 1.4;
    --line-height--body-loose: 1.6;

    /* Typography presets */
    {% liquid
      assign font_sizes = "paragraph, h1, h2, h3, h4, h5, h6" | split: ", "
      assign fluid_size_cutoff = 48
      assign absolute_font_size_min = 10

      comment
        Build an array of font sizes and sort it
      endcomment
      assign font_size_values = ''
      for font_size in font_sizes
        assign size_setting = 'type_size_[font_size]' | replace: '[font_size]', font_size
        assign size_setting_value = settings[size_setting] | times: 1

        comment
          If the font size is less than 100, pad it with a 0
          This is because we end up with an array of strings, which | sort filter can't order "correctly")
        endcomment
        if size_setting_value < 100
          assign size_setting_value = '0[size_setting_value]' | replace: '[size_setting_value]', size_setting_value
        endif

        assign font_size_values = font_size_values | append: '[size_setting_value],' | replace: '[size_setting_value]', size_setting_value
      endfor

      assign font_size_values = font_size_values | split: ',' | uniq | sort_natural

      comment
        For each font size S, find the next smaller size S-1, and determine the minimum for S
        The calculation depends on the size of S-1 (over or under the cutoff)
      endcomment
      for font_size in font_sizes
        assign size_setting = 'type_size_[font_size]' | replace: '[font_size]', font_size
        assign font_size_value = settings[size_setting] | times: 1
        assign font_size_string = '[font_size_value]' | replace: '[font_size_value]', font_size_value
        assign index = font_size_values | find_index: font_size_string

        if font_size_value >= fluid_size_cutoff

          comment
            Calculate the minimum size for each font size
          endcomment
          assign fluid_font_size_min = font_size_value

          if index == 0
            assign fluid_font_size_min = absolute_font_size_min
          else
            assign next_font_size_index = index | minus: 1
            assign next_font_size_value = font_size_values[next_font_size_index]
            assign next_font_size_value_number = next_font_size_value | times: 1

            comment
              If the next bigger font size under the fluid cutoff, we use keep a 4px buffer
            endcomment
            if next_font_size_value_number < fluid_size_cutoff
              assign fluid_font_size_min = next_font_size_value_number | plus: 4
              if font_size_value < fluid_font_size_min
                assign fluid_font_size_min = font_size_value
              endif
            else
              assign fluid_font_size_min = next_font_size_value | times: 1
            endif
          endif

          comment
            Calculate the fluid and maximum size for each font size
          endcomment
          assign fluid_size_min_rem = fluid_font_size_min | divided_by: 16.0
          assign fluid_size = font_size_value | times: 0.1
          assign fluid_size_max_rem = font_size_value | divided_by: 16.0

          echo '--font-size--[font_size]: clamp([fluid_size_min_rem]rem, [fluid_size]vw, [fluid_size_max_rem]rem);' | replace: '[font_size]', font_size | replace: '[fluid_size_min_rem]', fluid_size_min_rem | replace: '[fluid_size]', fluid_size | replace: '[fluid_size_max_rem]', fluid_size_max_rem
        else
          assign fluid_size_rem = font_size_value | divided_by: 16.0
          echo '--font-size--[font_size]: [fluid_size_rem]rem;' | replace: '[font_size]', font_size | replace: '[fluid_size_rem]', fluid_size_rem
        endif
      endfor

      assign type_presets = "paragraph, h1, h2, h3, h4, h5, h6" | split: ", "

      for preset_name in type_presets
        assign preset_size = '--font-size--[preset_name]' | replace: '[preset_name]', preset_name
        assign preset_line_height = 'type_line_height_[preset_name]' | replace: '[preset_name]', preset_name

        if preset_name == 'paragraph'
          assign preset_font = '--font-body--family'
          assign preset_style = '--font-body--style'
          assign preset_weight = '400'
          assign preset_case = '--font-body--case'
          assign preset_letter_spacing = 'body-normal'

          echo '--font-[preset_name]--weight: [preset_weight];' | replace: '[preset_name]', preset_name | replace: '[preset_weight]', preset_weight
          echo '--font-[preset_name]--letter-spacing: var(--letter-spacing--[preset_letter_spacing]);' | replace: '[preset_name]', preset_name | replace: '[preset_letter_spacing]', preset_letter_spacing
        else
          assign preset_font_id = 'type_font_[preset_name]' | replace: '[preset_name]', preset_name
          assign preset_font = '--font-[preset_font]--family' | replace: '[preset_font]', settings[preset_font_id]
          assign preset_style = '--font-[preset_font]--style' | replace: '[preset_font]', settings[preset_font_id]
          assign preset_weight = '--font-[preset_font]--weight' | replace: '[preset_font]', settings[preset_font_id]
          assign preset_case = 'type_case_[preset_name]' | replace: '[preset_name]', preset_name
          assign preset_letter_spacing = 'type_letter_spacing_[preset_name]' | replace: '[preset_name]', preset_name

          echo '--font-[preset_name]--weight: var([preset_weight]);' | replace: '[preset_name]', preset_name | replace: '[preset_weight]', preset_weight
          echo '--font-[preset_name]--letter-spacing: var(--letter-spacing--[preset_letter_spacing]);' | replace: '[preset_name]', preset_name | replace: '[preset_letter_spacing]', settings[preset_letter_spacing]
        endif

        echo '--font-[preset_name]--size: var([preset_size]);' | replace: '[preset_name]', preset_name | replace: '[preset_size]', preset_size
        echo '--font-[preset_name]--family: var([preset_font]);' | replace: '[preset_name]', preset_name | replace: '[preset_font]', preset_font
        echo '--font-[preset_name]--style: var([preset_style]);' | replace: '[preset_name]', preset_name | replace: '[preset_style]', preset_style
        echo '--font-[preset_name]--case: [preset_case];' | replace: '[preset_name]', preset_name | replace: '[preset_case]', settings[preset_case]
        echo '--font-[preset_name]--line-height: var(--line-height--[preset_line_height]);' | replace: '[preset_name]', preset_name | replace: '[preset_line_height]', settings[preset_line_height]
      endfor
    %}

    /* Hardcoded font sizes */
    --font-size--2xs: 0.625rem;
    --font-size--xs: 0.8125rem;
    --font-size--sm: 0.875rem;
    --font-size--md: 1rem;
    --font-size--lg: 1.125rem;
    --font-size--xl: 1.25rem;
    --font-size--2xl: 1.5rem;
    --font-size--3xl: 2rem;
    --font-size--4xl: 2.5rem;
    --font-size--5xl: 3rem;
    --font-size--6xl: 3.5rem;

    /* Menu font sizes */
    --menu-font-sm--size: 0.875rem;
    --menu-font-sm--line-height: calc(1.1 + 0.5 * min(16 / 14));
    --menu-font-md--size: 1rem;
    --menu-font-md--line-height: calc(1.1 + 0.5 * min(16 / 16));
    --menu-font-lg--size: 1.125rem;
    --menu-font-lg--line-height: calc(1.1 + 0.5 * min(16 / 18));
    --menu-font-xl--size: 1.25rem;
    --menu-font-xl--line-height: calc(1.1 + 0.5 * min(16 / 20));
    --menu-font-2xl--size: 1.75rem;
    --menu-font-2xl--line-height: calc(1.1 + 0.5 * min(16 / 28));

    /* Colors */
    --color-error: #8B0000;
    --color-success: #006400;
    --color-white: #FFFFFF;
    --color-black: #000000;
    --color-instock: #3ED660;
    --color-lowstock: #EE9441;
    --color-outofstock: #C8C8C8;

    /* Opacity */
    --opacity-subdued-text: 0.7;

    --shadow-button: 0 2px 3px rgba(0 0 0 / 20%);
    --gradient-image-overlay: linear-gradient(to top, rgb(from var(--color-black) r g b / 0.5), transparent);

    /* Spacing */
    --margin-3xs: 0.125rem;
    --margin-2xs: 0.3rem;
    --margin-xs: 0.5rem;
    --margin-sm: 0.7rem;
    --margin-md: 0.8rem;
    --margin-lg: 1rem;
    --margin-xl: 1.25rem;
    --margin-2xl: 1.5rem;
    --margin-3xl: 1.75rem;
    --margin-4xl: 2rem;
    --margin-5xl: 3rem;
    --margin-6xl: 5rem;

    --scroll-margin: 50px;

    --padding-3xs: 0.125rem;
    --padding-2xs: 0.25rem;
    --padding-xs: 0.5rem;
    --padding-sm: 0.7rem;
    --padding-md: 0.8rem;
    --padding-lg: 1rem;
    --padding-xl: 1.25rem;
    --padding-2xl: 1.5rem;
    --padding-3xl: 1.75rem;
    --padding-4xl: 2rem;
    --padding-5xl: 3rem;
    --padding-6xl: 4rem;

    --gap-3xs: 0.125rem;
    --gap-2xs: 0.3rem;
    --gap-xs: 0.5rem;
    --gap-sm: 0.7rem;
    --gap-md: 0.9rem;
    --gap-lg: 1rem;
    --gap-xl: 1.25rem;
    --gap-2xl: 2rem;
    --gap-3xl: 3rem;

    --spacing-scale-sm: 0.6;
    --spacing-scale-md: 0.7;
    --spacing-scale-default: 1.0;

    /* Checkout buttons gap */
    --checkout-button-gap: 8px;

    /* Borders */
    --style-border-width: 1px;
    --style-border-radius-xs: 0.2rem;
    --style-border-radius-sm: 0.6rem;
    --style-border-radius-md: 0.8rem;
    --style-border-radius-50: 50%;
    --style-border-radius-lg: 1rem;
    --style-border-radius-pills: {{ settings.pills_border_radius }}px;
    --style-border-radius-inputs: {{ settings.inputs_border_radius }}px;
    --style-border-radius-buttons-primary: {{ settings.button_border_radius_primary }}px;
    --style-border-radius-buttons-secondary: {{ settings.button_border_radius_secondary }}px;
    --style-border-width-primary: {{ settings.primary_button_border_width }}px;
    --style-border-width-secondary: {{ settings.secondary_button_border_width }}px;
    --style-border-width-inputs: {{ settings.input_border_width }}px;
    --style-border-radius-popover: {{ settings.popover_border_radius }}px;
    --style-border-popover: {{ settings.popover_border_width }}px {{ settings.popover_border }} rgb(from var(--color-border) r g b / {{ settings.popover_border_opacity }}%);
    --style-border-drawer: {{ settings.drawer_border_width }}px {{ settings.drawer_border }} rgb(from var(--color-border) r g b / {{ settings.drawer_border_opacity }}%);
    --style-border-swatch-opacity: {{ settings.variant_swatch_border_opacity }}%;
    --style-border-swatch-width: {{ settings.variant_swatch_border_width }}px;
    --style-border-swatch-style: {{ settings.variant_swatch_border_style }};

    /* Animation */
    --ease-out-cubic: cubic-bezier(0.33, 1, 0.68, 1);
    --ease-out-quad: cubic-bezier(0.32, 0.72, 0, 1);
    --animation-speed: 0.125s;
    --animation-speed-slow: 0.2s;
    --animation-speed-medium: 0.15s;
    --animation-easing: ease-in-out;
    --animation-slideshow-easing: cubic-bezier(0.4, 0, 0.2, 1);
    --drawer-animation-speed: 0.2s;
    --animation-values: var(--animation-speed) var(--animation-easing);
    --animation-values-allow-discrete: var(--animation-speed) var(--animation-easing) allow-discrete;
    --animation-timing-hover: cubic-bezier(0.25, 0.46, 0.45, 0.94);
    --animation-timing-active: cubic-bezier(0.5, 0, 0.75, 0);
    --animation-timing-bounce: cubic-bezier(0.34, 1.56, 0.64, 1);
    --animation-timing-default: cubic-bezier(0, 0, 0.2, 1);
    --animation-timing-fade-in: cubic-bezier(0.16, 1, 0.3, 1);
    --animation-timing-fade-out: cubic-bezier(0.4, 0, 0.2, 1);

    /* View transitions */
    /* View transition old */
    --view-transition-old-main-content: var(--animation-speed) var(--animation-easing) both fadeOut;

    /* View transition new */
    --view-transition-new-main-content: var(--animation-speed) var(--animation-easing) both fadeIn, var(--animation-speed) var(--animation-easing) both slideInTopViewTransition;

    /* Focus */
    --focus-outline-width: 0.09375rem;
    --focus-outline-offset: 0.2em;

    /* Badges */
    --badge-blob-padding-block: 1px;
    --badge-blob-padding-inline: 12px 8px;
    --badge-rectangle-padding-block: 1px;
    --badge-rectangle-padding-inline: 6px;
    @media screen and (min-width: 750px) {
      --badge-blob-padding-block: 4px;
      --badge-blob-padding-inline: 16px 12px;
      --badge-rectangle-padding-block: 4px;
      --badge-rectangle-padding-inline: 10px;
    }

    /* Icons */
    --icon-size-2xs: 0.6rem;
    --icon-size-xs: 0.85rem;
    --icon-size-sm: 1.25rem;
    --icon-size-md: 1.375rem;
    --icon-size-lg: 1.5rem;
    --icon-stroke-width: {% if settings.icon_stroke == 'thin' %}1px{% elsif settings.icon_stroke == 'heavy' %}2px{% else %}1.5px{% endif %};

    /* Input */
    --input-email-min-width: 200px;
    --input-search-max-width: 650px;
    --input-padding-y: 0.8rem;
    --input-padding-x: 0.8rem;
    --input-padding: var(--input-padding-y) var(--input-padding-x);
    --input-box-shadow-width: var(--style-border-width-inputs);
    --input-box-shadow: 0 0 0 var(--input-box-shadow-width) var(--color-input-border);
    --input-box-shadow-focus: 0 0 0 calc(var(--input-box-shadow-width) + 0.5px) var(--color-input-border);
    --input-disabled-background-color: rgb(from var(--color-foreground) r g b / 10%);
    --input-disabled-border-color: rgb(from var(--color-foreground) r g b / 5%);
    --input-disabled-text-color: rgb(from var(--color-foreground) r g b / 50%);
    --input-textarea-min-height: 55px;

    /* Button size */
    --button-size-sm: 30px;
    --button-size-md: 36px;
    --button-size: var(--minimum-touch-target);
    --button-padding-inline: 24px;
    --button-padding-block: 16px;

    /* Button font-family */
    --button-font-family-primary: var(--font-{{ settings.type_font_button_primary }}--family);
    --button-font-family-secondary: var(--font-{{ settings.type_font_button_secondary }}--family);

    /* Button font-weight */
    --button-font-weight-primary: {{ settings.button_font_weight_primary }};
    --button-font-weight-secondary: {{ settings.button_font_weight_secondary }};

    /* Button text case */
    --button-text-case: {{ settings.button_text_case }};
    --button-text-case-primary: {{ settings.button_text_case_primary }};
    --button-text-case-secondary: {{ settings.button_text_case_secondary }};

    /* Borders */
    --border-color: rgba(from var(--color-border) r g b / 0.55);
    --border-width-sm: 1px;
    --border-width-md: 2px;
    --border-width-lg: 5px;

    /* Drawers */
    --drawer-inline-padding: 25px;
    --drawer-menu-inline-padding: 2.5rem;
    --drawer-header-block-padding: 20px;
    --drawer-content-block-padding: 10px;
    --drawer-header-desktop-top: 0rem;
    --drawer-padding: calc(var(--padding-sm) + 7px);
    --drawer-height: 100dvh;
    --drawer-width: 95vw;
    --drawer-max-width: 500px;

    /* Variant Picker Swatches */
    --variant-picker-swatch-width-unitless: {{ settings.variant_swatch_width }};
    --variant-picker-swatch-height-unitless: {{ settings.variant_swatch_height }};
    --variant-picker-swatch-width: {{ settings.variant_swatch_width | append: 'px' }};
    --variant-picker-swatch-height: {{ settings.variant_swatch_height | append: 'px' }};
    --variant-picker-swatch-radius: {{ settings.variant_swatch_radius | append: 'px' }};
    --variant-picker-border-width: {{ settings.variant_swatch_border_width | append: 'px' }};
    --variant-picker-border-style: {{ settings.variant_swatch_border_style }};
    --variant-picker-border-opacity: {{ settings.variant_swatch_border_opacity | append: '%' }};

    /* Variant Picker Buttons */
    --variant-picker-button-radius: {{ settings.variant_button_radius | append: 'px' }};
    --variant-picker-button-border-width: {{ settings.variant_button_border_width | append: 'px' }};

    /* Slideshow */
    --slideshow-controls-size: 3.5rem;
    --slideshow-controls-icon: 2rem;
    --peek-next-slide-size: 3rem;

    /* Utilities */
    --backdrop-opacity: 0.15;
    --backdrop-color: var(--color-shadow);
    --minimum-touch-target: 44px;
    --disabled-opacity: 0.5;
    --skeleton-opacity: 0.025;

    /* Shapes */
    --shape--circle: circle(50% at center);
    --shape--sunburst: polygon(100% 50%,94.62% 55.87%,98.3% 62.94%,91.57% 67.22%,93.3% 75%,85.7% 77.39%,85.36% 85.36%,77.39% 85.7%,75% 93.3%,67.22% 91.57%,62.94% 98.3%,55.87% 94.62%,50% 100%,44.13% 94.62%,37.06% 98.3%,32.78% 91.57%,25% 93.3%,22.61% 85.7%,14.64% 85.36%,14.3% 77.39%,6.7% 75%,8.43% 67.22%,1.7% 62.94%,5.38% 55.87%,0% 50%,5.38% 44.13%,1.7% 37.06%,8.43% 32.78%,6.7% 25%,14.3% 22.61%,14.64% 14.64%,22.61% 14.3%,25% 6.7%,32.78% 8.43%,37.06% 1.7%,44.13% 5.38%,50% 0%,55.87% 5.38%,62.94% 1.7%,67.22% 8.43%,75% 6.7%,77.39% 14.3%,85.36% 14.64%,85.7% 22.61%,93.3% 25%,91.57% 32.78%,98.3% 37.06%,94.62% 44.13%);
    --shape--diamond: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);
    --shape--blob: polygon(85.349% 11.712%, 87.382% 13.587%, 89.228% 15.647%, 90.886% 17.862%, 92.359% 20.204%, 93.657% 22.647%, 94.795% 25.169%, 95.786% 27.752%, 96.645% 30.382%, 97.387% 33.048%, 98.025% 35.740%, 98.564% 38.454%, 99.007% 41.186%, 99.358% 43.931%, 99.622% 46.685%, 99.808% 49.446%, 99.926% 52.210%, 99.986% 54.977%, 99.999% 57.744%, 99.975% 60.511%, 99.923% 63.278%, 99.821% 66.043%, 99.671% 68.806%, 99.453% 71.565%, 99.145% 74.314%, 98.724% 77.049%, 98.164% 79.759%, 97.433% 82.427%, 96.495% 85.030%, 95.311% 87.529%, 93.841% 89.872%, 92.062% 91.988%, 89.972% 93.796%, 87.635% 95.273%, 85.135% 96.456%, 82.532% 97.393%, 79.864% 98.127%, 77.156% 98.695%, 74.424% 99.129%, 71.676% 99.452%, 68.918% 99.685%, 66.156% 99.844%, 63.390% 99.942%, 60.624% 99.990%, 57.856% 99.999%, 55.089% 99.978%, 52.323% 99.929%, 49.557% 99.847%, 46.792% 99.723%, 44.031% 99.549%, 41.273% 99.317%, 38.522% 99.017%, 35.781% 98.639%, 33.054% 98.170%, 30.347% 97.599%, 27.667% 96.911%, 25.024% 96.091%, 22.432% 95.123%, 19.907% 93.994%, 17.466% 92.690%, 15.126% 91.216%, 12.902% 89.569%, 10.808% 87.761%, 8.854% 85.803%, 7.053% 83.703%, 5.418% 81.471%, 3.962% 79.119%, 2.702% 76.656%, 1.656% 74.095%, 0.846% 71.450%, 0.294% 68.740%, 0.024% 65.987%, 0.050% 63.221%, 0.343% 60.471%, 0.858% 57.752%, 1.548% 55.073%, 2.370% 52.431%, 3.283% 49.819%, 4.253% 47.227%, 5.249% 44.646%, 6.244% 42.063%, 7.211% 39.471%, 8.124% 36.858%, 8.958% 34.220%, 9.711% 31.558%, 10.409% 28.880%, 11.083% 26.196%, 11.760% 23.513%, 12.474% 20.839%, 13.259% 18.186%, 14.156% 15.569%, 15.214% 13.012%, 16.485% 10.556%, 18.028% 8.261%, 19.883% 6.211%, 22.041% 4.484%, 24.440% 3.110%, 26.998% 2.057%, 29.651% 1.275%, 32.360% 0.714%, 35.101% 0.337%, 37.859% 0.110%, 40.624% 0.009%, 43.391% 0.016%, 46.156% 0.113%, 48.918% 0.289%, 51.674% 0.533%, 54.425% 0.837%, 57.166% 1.215%, 59.898% 1.654%, 62.618% 2.163%, 65.322% 2.750%, 68.006% 3.424%, 70.662% 4.197%, 73.284% 5.081%, 75.860% 6.091%, 78.376% 7.242%, 80.813% 8.551%, 83.148% 10.036%, 85.349% 11.712%);

    /* Buy buttons */
    --height-buy-buttons: calc(var(--padding-lg) * 2 + var(--icon-size-sm));

    /* Card image height variables */
    --height-small: 10rem;
    --height-medium: 11.5rem;
    --height-large: 13rem;
    --height-full: 100vh;

    @media screen and (min-width: 750px) {
      --height-small: 17.5rem;
      --height-medium: 21.25rem;
      --height-large: 25rem;
    }

    /* Modal */
    --modal-max-height: 65dvh;

    /* Card styles for search */
    --card-bg-hover: rgb(from var(--color-foreground) r g b / 0.03);
    --card-border-hover: rgb(from var(--color-foreground) r g b / 0.3);
    --card-border-focus: rgb(from var(--color-border) r g b / 0.1);

    /* Cart */
    --cart-primary-font-family: var(--font-body--family);
    --cart-primary-font-style: var(--font-body--style);
    --cart-primary-font-weight: var(--font-body--weight);
    --cart-secondary-font-family: var(--font-{{ settings.cart_price_font }}--family);
    --cart-secondary-font-style: var(--font-{{ settings.cart_price_font }}--style);
    --cart-secondary-font-weight: var(--font-{{ settings.cart_price_font }}--weight);
  }
{% endstyle %}

{%- doc -%}
  Intended for use in a block similar to the text block.

  @param {string} [class] - custom class to define in addition to text-block classes
  @param {string} [fallback_text] - fallback text if settings.text does not exist
  @param {string} [width] - width of the text block
{%- enddoc -%}

{% liquid
  assign plain_text = block.settings.text | strip_newlines | strip_html | strip
  assign text_width = width | default: block.settings.width

  if block.settings.font_size contains 'heading-lg' or block.settings.font_size contains 'heading-xl'
    assign type = 'display'
  elsif block.settings.font_size contains 'heading'
    assign type = 'heading'
  else
    assign type = 'body'
  endif
  if block.settings.type_preset == 'rte' or block.settings.type_preset == 'paragraph'
    assign is_rte = true
  endif

  capture text_block_classes
    if text_width == '100%'
      echo 'text-block--align-' | append: block.settings.alignment
      if block.settings.max_width == 'none'
        echo ' text-block--full-width '
      endif
    endif
    if block.settings.type_preset == 'custom'
      echo ' custom-typography '
      if block.settings.font_size != ''
        echo ' custom-font-size '
      endif
      if block.settings.color != ''
        echo ' custom-color '
      endif
    endif
    if block.settings.background
      echo ' text-block--background '
    endif
    if is_rte
      echo ' rte '
    endif
  endcapture
%}

{% capture attributes %}
  class="{{ class }} spacing-style text-block text-block--{{ block.id }} {{ block.settings.type_preset }}
    {{ text_block_classes }}
  "

  style="
    {% render 'spacing-padding', settings: block.settings %}
    {% render 'typography-style', settings: block.settings %}
    --width: {{ text_width }};
    --max-width: var(--max-width--{{ type }}-{{ block.settings.max_width }});
    {% if text_width == "100%" %}
      --text-align: {{ block.settings.alignment }};
    {% endif %}
    {% if block.settings.background %}
      --text-background-color: {{ block.settings.background_color | default: 'rgb(255 255 255 / 1.0)' }};
      --text-corner-radius: {{ block.settings.corner_radius }}px;
      --text-padding: max(var(--padding-2xs), calc((var(--text-corner-radius) + var(--padding-xs)) * (1 - cos(45deg))));
    {% endif %}
  "

  {{ block.shopify_attributes }}
{% endcapture %}
{% liquid
  # {{ attributes }} must be on the immediate HTML parent of the text to preserve
  # the click-to-edit connection in the theme editor. Any break between the text,
  # including if-statements, will break the connection.

  assign element = 'div'
  if is_rte
    assign element = 'rte-formatter'
  endif
%}

{% if fallback_text != blank and plain_text == blank %}
  <div {{ attributes }}>
    {{ fallback_text }}
  </div>
{% elsif plain_text != blank %}
  <{{ element }} {{ attributes }}>
    {{ block.settings.text }}
  </{{ element }}>
{% endif %}

{% stylesheet %}
  :root {
    --text-align-default: left;
  }

  [style*='--horizontal-alignment: center'] .text-block {
    --text-align-default: center;
  }

  [style*='--horizontal-alignment: flex-end'] .text-block {
    --text-align-default: right;
  }

  [style*='--horizontal-alignment: flex-start'] > .text-block {
    --text-align-default: left;
  }

  [style*='--horizontal-alignment: center'] > .text-block {
    --text-align-default: center;
  }

  [style*='--horizontal-alignment: flex-end'] > .text-block {
    --text-align-default: right;
  }

  .text-block {
    width: var(--width);
    max-width: 100%;
    display: flex;
    flex-direction: column;
    align-items: var(--horizontal-alignment);
  }

  .text-block > * {
    width: var(--width);
    max-width: var(--max-width, 100%);
    text-align: var(--text-align, var(--text-align-default));
    text-wrap: var(--text-wrap);
  }

  .text-block:not(.text-block--full-width).rte,
  .text-block:not(.text-block--full-width).paragraph {
    /* Safari doesn't support pretty, so fallback to balance */
    text-wrap: balance;
    text-wrap: pretty;
  }

  .text-block:not(.text-block--full-width):is(.h1, .h2, .h3, .h4, .h5, .h6) {
    text-wrap: balance;
  }

  /* Hide underline unless text is using paragraph styles. */
  .text-block:is(.h1, .h2, .h3, .h4, .h5, .h6) a {
    text-decoration-color: transparent;
  }

  .text-block h1,
  .text-block.h1 > * {
    margin-block: var(--font-h1--spacing);
  }

  .text-block h2,
  .text-block.h2 > * {
    margin-block: var(--font-h2--spacing);
  }

  .text-block h3,
  .text-block.h3 > * {
    margin-block: var(--font-h3--spacing);
  }

  .text-block h4,
  .text-block.h4 > * {
    margin-block: var(--font-h4--spacing);
  }

  .text-block h5,
  .text-block.h5 > * {
    margin-block: var(--font-h5--spacing);
  }

  .text-block h6,
  .text-block.h6 > * {
    margin-block: var(--font-h6--spacing);
  }

  .text-block p,
  .text-block.p > * {
    margin-block: var(--font-paragraph--spacing);
  }

  .text-block > *:first-child {
    margin-block-start: 0;
  }

  .text-block > *:last-child {
    margin-block-end: 0;
  }

  .text-block--align-center,
  .text-block--align-center > * {
    margin-inline: auto;
  }

  .text-block--align-right,
  .text-block--align-right > * {
    margin-inline-start: auto;
  }

  .text-block--background {
    background-color: var(--text-background-color);
    border-radius: var(--text-corner-radius);

    /* To avoid text being cropped when using a border radius we add a minimum padding. */
    padding-block-start: max(var(--text-padding), var(--padding-block-start, 0));
    padding-block-end: max(var(--text-padding), var(--padding-block-end, 0));
    padding-inline-start: max(var(--text-padding), var(--padding-inline-start, 0));
    padding-inline-end: max(var(--text-padding), var(--padding-inline-end, 0));
  }

  .custom-color,
  .custom-color > :is(h1, h2, h3, h4, h5, h6, p, *) {
    color: var(--color);
  }
{% endstylesheet %}

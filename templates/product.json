/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "17533432653b3c2f79": {
      "type": "_blocks",
      "settings": {
        "content_direction": "column",
        "vertical_on_mobile": true,
        "horizontal_alignment": "flex-start",
        "vertical_alignment": "center",
        "align_baseline": false,
        "horizontal_alignment_flex_direction_column": "flex-start",
        "vertical_alignment_flex_direction_column": "center",
        "gap": 12,
        "section_width": "page-width",
        "section_height": "",
        "section_height_custom": 50,
        "color_scheme": "",
        "background_media": "none",
        "video_position": "cover",
        "background_image_position": "cover",
        "border": "none",
        "border_width": 1,
        "border_opacity": 100,
        "border_radius": 0,
        "toggle_overlay": false,
        "overlay_color": "#00000026",
        "overlay_style": "solid",
        "gradient_direction": "to top",
        "padding-block-start": 0,
        "padding-block-end": 0
      }
    },
    "main": {
      "type": "product-information",
      "blocks": {
        "media-gallery": {
          "type": "_product-media-gallery",
          "static": true,
          "settings": {
            "media_presentation": "carousel",
            "media_columns": "two",
            "image_gap": 1,
            "large_first_image": false,
            "icons_style": "arrows",
            "slideshow_controls_style": "thumbnails",
            "slideshow_mobile_controls_style": "dots",
            "thumbnail_position": "bottom",
            "thumbnail_width": 48,
            "aspect_ratio": "1",
            "media_radius": 0,
            "extend_media": false,
            "constrain_to_viewport": true,
            "zoom": true,
            "video_loop": true,
            "hide_variants": false,
            "padding-block-start": 0,
            "padding-block-end": 0,
            "padding-inline-start": 0,
            "padding-inline-end": 0
          },
          "blocks": {}
        },
        "product-details": {
          "type": "_product-details",
          "static": true,
          "settings": {
            "width": "fill",
            "custom_width": 75,
            "width_mobile": "fill",
            "custom_width_mobile": 100,
            "height": "fit",
            "details_position": "flex-start",
            "gap": 20,
            "sticky_details_desktop": true,
            "inherit_color_scheme": true,
            "color_scheme": "",
            "background_media": "none",
            "video_position": "cover",
            "background_image_position": "cover",
            "border": "none",
            "border_width": 1,
            "border_opacity": 100,
            "border_radius": 0,
            "padding-block-start": 18,
            "padding-block-end": 48,
            "padding-inline-start": 0,
            "padding-inline-end": 0
          },
          "blocks": {
            "group_JtXipy": {
              "type": "group",
              "name": "t:names.group",
              "settings": {
                "link": "",
                "open_in_new_tab": false,
                "content_direction": "column",
                "vertical_on_mobile": true,
                "horizontal_alignment": "flex-start",
                "vertical_alignment": "center",
                "align_baseline": false,
                "horizontal_alignment_flex_direction_column": "flex-start",
                "vertical_alignment_flex_direction_column": "center",
                "gap": 28,
                "width": "fill",
                "custom_width": 65,
                "width_mobile": "fill",
                "custom_width_mobile": 100,
                "height": "fit",
                "custom_height": 100,
                "inherit_color_scheme": true,
                "color_scheme": "",
                "background_media": "none",
                "video_position": "cover",
                "background_image_position": "cover",
                "border": "none",
                "border_width": 1,
                "border_opacity": 100,
                "border_radius": 0,
                "toggle_overlay": false,
                "overlay_color": "#00000026",
                "overlay_style": "solid",
                "gradient_direction": "to top",
                "padding-block-start": 40,
                "padding-block-end": 0,
                "padding-inline-start": 0,
                "padding-inline-end": 0
              },
              "blocks": {
                "group_xr9GGL": {
                  "type": "group",
                  "name": "Header",
                  "settings": {
                    "link": "",
                    "open_in_new_tab": false,
                    "content_direction": "column",
                    "vertical_on_mobile": true,
                    "horizontal_alignment": "flex-start",
                    "vertical_alignment": "flex-start",
                    "align_baseline": false,
                    "horizontal_alignment_flex_direction_column": "flex-start",
                    "vertical_alignment_flex_direction_column": "center",
                    "gap": 8,
                    "width": "fill",
                    "custom_width": 100,
                    "width_mobile": "fill",
                    "custom_width_mobile": 100,
                    "height": "fit",
                    "custom_height": 100,
                    "inherit_color_scheme": true,
                    "color_scheme": "",
                    "background_media": "none",
                    "video_position": "cover",
                    "background_image_position": "cover",
                    "border": "none",
                    "border_width": 1,
                    "border_opacity": 100,
                    "border_radius": 0,
                    "toggle_overlay": false,
                    "overlay_color": "#00000026",
                    "overlay_style": "solid",
                    "gradient_direction": "to top",
                    "padding-block-start": 0,
                    "padding-block-end": 0,
                    "padding-inline-start": 0,
                    "padding-inline-end": 0
                  },
                  "blocks": {
                    "text_C373zj": {
                      "type": "text",
                      "settings": {
                        "text": "<h1>{{ closest.product.title }}</h1>",
                        "width": "100%",
                        "max_width": "normal",
                        "alignment": "left",
                        "type_preset": "h2",
                        "font": "var(--font-primary--family)",
                        "font_size": "",
                        "line_height": "normal",
                        "letter_spacing": "normal",
                        "case": "none",
                        "wrap": "pretty",
                        "color": "",
                        "background": false,
                        "background_color": "#00000026",
                        "corner_radius": 0,
                        "padding-block-start": 0,
                        "padding-block-end": 0,
                        "padding-inline-start": 0,
                        "padding-inline-end": 0
                      },
                      "blocks": {}
                    },
                    "group_K8TE9x": {
                      "type": "group",
                      "name": "Price & Rating",
                      "settings": {
                        "link": "",
                        "open_in_new_tab": false,
                        "content_direction": "row",
                        "vertical_on_mobile": true,
                        "horizontal_alignment": "flex-start",
                        "vertical_alignment": "flex-start",
                        "align_baseline": false,
                        "horizontal_alignment_flex_direction_column": "flex-start",
                        "vertical_alignment_flex_direction_column": "center",
                        "gap": 24,
                        "width": "fill",
                        "custom_width": 100,
                        "width_mobile": "fill",
                        "custom_width_mobile": 100,
                        "height": "fit",
                        "custom_height": 100,
                        "inherit_color_scheme": true,
                        "color_scheme": "",
                        "background_media": "none",
                        "video_position": "cover",
                        "background_image_position": "cover",
                        "border": "none",
                        "border_width": 1,
                        "border_opacity": 100,
                        "border_radius": 0,
                        "toggle_overlay": false,
                        "overlay_color": "#00000026",
                        "overlay_style": "solid",
                        "gradient_direction": "to top",
                        "padding-block-start": 0,
                        "padding-block-end": 0,
                        "padding-inline-start": 0,
                        "padding-inline-end": 0
                      },
                      "blocks": {
                        "price_WRfkDh": {
                          "type": "price",
                          "settings": {
                            "show_sale_price_first": true,
                            "show_installments": true,
                            "show_tax_info": true,
                            "type_preset": "h5",
                            "width": "100%",
                            "alignment": "left",
                            "font": "var(--font-body--family)",
                            "font_size": "1rem",
                            "line_height": "normal",
                            "letter_spacing": "normal",
                            "case": "none",
                            "color": "var(--color-foreground)",
                            "padding-block-start": 4,
                            "padding-block-end": 0,
                            "padding-inline-start": 0,
                            "padding-inline-end": 0
                          },
                          "blocks": {}
                        },
                        "review_m3RVcq": {
                          "type": "review",
                          "settings": {
                            "stars_style": "shaded",
                            "show_number": true,
                            "rating_color": "primary",
                            "type_preset": "paragraph",
                            "alignment": "left"
                          },
                          "blocks": {}
                        }
                      },
                      "block_order": [
                        "price_WRfkDh",
                        "review_m3RVcq"
                      ]
                    }
                  },
                  "block_order": [
                    "text_C373zj",
                    "group_K8TE9x"
                  ]
                },
                "divider_grFKCP": {
                  "type": "_divider",
                  "name": "t:names.divider",
                  "settings": {
                    "thickness": 1,
                    "corner_radius": "square",
                    "width_percent": 100,
                    "padding-block-start": 0,
                    "padding-block-end": 0
                  },
                  "blocks": {}
                },
                "product_description_8gHdXj": {
                  "type": "product-description",
                  "name": "Product description",
                  "settings": {
                    "text": "<p>{{ closest.product.description }}</p>",
                    "width": "100%",
                    "max_width": "normal",
                    "alignment": "left",
                    "type_preset": "paragraph",
                    "font": "var(--font-primary--family)",
                    "font_size": "",
                    "line_height": "normal",
                    "letter_spacing": "normal",
                    "case": "none",
                    "wrap": "pretty",
                    "color": "",
                    "background": false,
                    "background_color": "#00000026",
                    "corner_radius": 0,
                    "padding-block-start": 0,
                    "padding-block-end": 0,
                    "padding-inline-start": 0,
                    "padding-inline-end": 0
                  },
                  "blocks": {}
                },
                "variant_picker_nhbWcG": {
                  "type": "variant-picker",
                  "settings": {
                    "variant_style": "buttons",
                    "show_swatches": true,
                    "alignment": "left",
                    "padding-block-start": 0,
                    "padding-block-end": 0,
                    "padding-inline-start": 0,
                    "padding-inline-end": 0
                  },
                  "blocks": {}
                },
                "buy_buttons_B7HMzq": {
                  "type": "buy-buttons",
                  "settings": {
                    "stacking": false,
                    "show_pickup_availability": true,
                    "padding-block-start": 0,
                    "padding-block-end": 0,
                    "padding-inline-start": 0,
                    "padding-inline-end": 0
                  },
                  "blocks": {
                    "quantity": {
                      "type": "quantity",
                      "static": true,
                      "settings": {},
                      "blocks": {}
                    },
                    "add-to-cart": {
                      "type": "add-to-cart",
                      "static": true,
                      "settings": {
                        "style_class": "button"
                      },
                      "blocks": {}
                    },
                    "accelerated-checkout": {
                      "type": "accelerated-checkout",
                      "static": true,
                      "settings": {},
                      "blocks": {}
                    }
                  },
                  "block_order": []
                },
                "fsa_hsa_banner": {
                  "type": "fsa-hsa-banner",
                  "settings": {
                    "title": "Use Your FSA/HSA Dollars Before You Lose Them",
                    "link_url": "",
                    "link_text": "Learn more",
                    "show_icon": true,
                    "background_color": "",
                    "text_color": "",
                    "box_shadow": "0 4px 12px rgba(0, 0, 0, 0.1)",
                    "border_radius": 12,
                    "padding_vertical": 20,
                    "padding_horizontal": 24,
                    "margin_top": 0,
                    "margin_bottom": 0,
                    "title_size": 20,
                    "title_weight": 600,
                    "link_size": 15,
                    "link_weight": 500
                  },
                  "blocks": {}
                },
                "about_product": {
                  "type": "about-product",
                  "settings": {
                    "heading": "About the product",
                    "description": "It's all about reducing fatigue and improving comfort! Power Walking & Hiking Upsteps smooth out the natural heel-to-toe 'roll' your feet make as you walk. They're individually designed for you from durable materials to prevent damage and reduce or eliminate pain.",
                    "background_color": "",
                    "heading_color": "",
                    "text_color": "",
                    "border_bottom_color": "",
                    "border_radius": 0,
                    "padding_vertical": 0,
                    "padding_horizontal": 0,
                    "padding_bottom": 24,
                    "margin_top": 0,
                    "margin_bottom": 0,
                    "border_bottom_width": 1,
                    "heading_size": 18,
                    "heading_weight": 600,
                    "heading_margin_bottom": 12,
                    "text_size": 15,
                    "text_weight": 400,
                    "text_line_height": 1.6
                  },
                  "blocks": {}
                },
                "benefits_icons": {
                  "type": "benefits-icons",
                  "settings": {
                    "benefit_1_title": "180-day money-back",
                    "benefit_1_emoji": "✓",
                    "benefit_2_title": "Free shipping",
                    "benefit_2_emoji": "🚚",
                    "benefit_3_title": "FSA/HSA Eligible",
                    "benefit_3_emoji": "💳",
                    "background_color": "#e6e6e6",
                    "title_color": "",
                    "emoji_color": "",
                    "alignment": "space-between",
                    "border_radius": 8,
                    "padding_vertical": 24,
                    "padding_horizontal": 20,
                    "margin_top": 0,
                    "margin_bottom": 0,
                    "gap": 16,
                    "icon_size": 42,
                    "title_size": 14,
                    "title_weight": 500,
                    "emoji_size": 20
                  },
                  "blocks": {}
                },
                "accordion_jjaG39": {
                  "type": "accordion",
                  "name": "t:names.accordion",
                  "settings": {
                    "icon": "plus",
                    "dividers": true,
                    "type_preset": "h5",
                    "inherit_color_scheme": true,
                    "color_scheme": "",
                    "border": "none",
                    "border_width": 1,
                    "border_opacity": 100,
                    "border_radius": 0,
                    "padding-block-start": 0,
                    "padding-block-end": 0,
                    "padding-inline-start": 0,
                    "padding-inline-end": 0
                  },
                  "blocks": {
                    "accordion_row_WyAjHK": {
                      "type": "_accordion-row",
                      "settings": {
                        "heading": "CARE & MAINTENANCE",
                        "open_by_default": false,
                        "icon": "none",
                        "width": 20
                      },
                      "blocks": {
                        "text_UfENHV": {
                          "type": "text",
                          "settings": {
                            "text": "<p>To maintain the beauty and integrity of your purchase, we recommend treating it with care. Simple maintenance practices, such as gentle washing and proper storage, can effectively preserve the longevity of your favorites. We encourage you to refer to the care instructions included with each item, designed to help you keep your purchase in top condition.</p>",
                            "width": "100%",
                            "max_width": "normal",
                            "alignment": "left",
                            "type_preset": "rte",
                            "font": "var(--font-body--family)",
                            "font_size": "1rem",
                            "line_height": "normal",
                            "letter_spacing": "normal",
                            "case": "none",
                            "wrap": "pretty",
                            "color": "var(--color-foreground)",
                            "background": false,
                            "background_color": "#00000026",
                            "corner_radius": 0,
                            "padding-block-start": 0,
                            "padding-block-end": 0,
                            "padding-inline-start": 0,
                            "padding-inline-end": 0
                          },
                          "blocks": {}
                        }
                      },
                      "block_order": [
                        "text_UfENHV"
                      ]
                    },
                    "accordion_row_xktep7": {
                      "type": "_accordion-row",
                      "settings": {
                        "heading": "SHIPPING & RETURNS",
                        "open_by_default": false,
                        "icon": "none",
                        "width": 20
                      },
                      "blocks": {
                        "text_6rQYtf": {
                          "type": "text",
                          "settings": {
                            "text": "<p>We strive to process and ship all orders in a timely manner, working diligently to ensure that your items are on their way to you as soon as possible. Need to return something? Just let us know. </p>",
                            "width": "100%",
                            "max_width": "normal",
                            "alignment": "left",
                            "type_preset": "rte",
                            "font": "var(--font-body--family)",
                            "font_size": "1rem",
                            "line_height": "normal",
                            "letter_spacing": "normal",
                            "case": "none",
                            "wrap": "pretty",
                            "color": "var(--color-foreground)",
                            "background": false,
                            "background_color": "#00000026",
                            "corner_radius": 0,
                            "padding-block-start": 0,
                            "padding-block-end": 0,
                            "padding-inline-start": 0,
                            "padding-inline-end": 0
                          },
                          "blocks": {}
                        }
                      },
                      "block_order": [
                        "text_6rQYtf"
                      ]
                    }
                  },
                  "block_order": [
                    "accordion_row_WyAjHK",
                    "accordion_row_xktep7"
                  ]
                }
              },
              "block_order": [
                "group_xr9GGL",
                "divider_grFKCP",
                "product_description_8gHdXj",
                "variant_picker_nhbWcG",
                "buy_buttons_B7HMzq",
                "fsa_hsa_banner",
                "about_product",
                "benefits_icons",
                "accordion_jjaG39"
              ]
            },
            "group": {
              "type": "group",
              "name": "Header",
              "disabled": true,
              "settings": {
                "link": "",
                "open_in_new_tab": false,
                "content_direction": "row",
                "vertical_on_mobile": true,
                "horizontal_alignment": "flex-start",
                "vertical_alignment": "flex-start",
                "align_baseline": false,
                "horizontal_alignment_flex_direction_column": "flex-start",
                "vertical_alignment_flex_direction_column": "center",
                "gap": 35,
                "width": "fill",
                "custom_width": 100,
                "width_mobile": "fill",
                "custom_width_mobile": 100,
                "height": "fit",
                "custom_height": 100,
                "inherit_color_scheme": true,
                "color_scheme": "",
                "background_media": "none",
                "video_position": "cover",
                "background_image_position": "cover",
                "border": "none",
                "border_width": 1,
                "border_opacity": 100,
                "border_radius": 0,
                "toggle_overlay": false,
                "overlay_color": "#00000026",
                "overlay_style": "solid",
                "gradient_direction": "to top",
                "padding-block-start": 0,
                "padding-block-end": 0,
                "padding-inline-start": 0,
                "padding-inline-end": 0
              },
              "blocks": {
                "group_Ejb3R7": {
                  "type": "group",
                  "name": "t:names.group",
                  "settings": {
                    "link": "",
                    "open_in_new_tab": false,
                    "content_direction": "column",
                    "vertical_on_mobile": true,
                    "horizontal_alignment": "flex-start",
                    "vertical_alignment": "center",
                    "align_baseline": false,
                    "horizontal_alignment_flex_direction_column": "flex-start",
                    "vertical_alignment_flex_direction_column": "center",
                    "gap": 12,
                    "width": "fit-content",
                    "custom_width": 100,
                    "width_mobile": "fill",
                    "custom_width_mobile": 100,
                    "height": "fit",
                    "custom_height": 100,
                    "inherit_color_scheme": true,
                    "color_scheme": "",
                    "background_media": "none",
                    "video_position": "cover",
                    "background_image_position": "cover",
                    "border": "none",
                    "border_width": 1,
                    "border_opacity": 100,
                    "border_radius": 0,
                    "toggle_overlay": false,
                    "overlay_color": "#00000026",
                    "overlay_style": "solid",
                    "gradient_direction": "to top",
                    "padding-block-start": 0,
                    "padding-block-end": 0,
                    "padding-inline-start": 0,
                    "padding-inline-end": 0
                  },
                  "blocks": {
                    "text_j4RAyy": {
                      "type": "text",
                      "settings": {
                        "text": "<h1>Subtitle</h1>",
                        "width": "fit-content",
                        "max_width": "normal",
                        "alignment": "left",
                        "type_preset": "h6",
                        "font": "var(--font-primary--family)",
                        "font_size": "",
                        "line_height": "normal",
                        "letter_spacing": "normal",
                        "case": "none",
                        "wrap": "pretty",
                        "color": "",
                        "background": false,
                        "background_color": "#00000026",
                        "corner_radius": 0,
                        "padding-block-start": 0,
                        "padding-block-end": 0,
                        "padding-inline-start": 0,
                        "padding-inline-end": 0
                      },
                      "blocks": {}
                    },
                    "text_gJNTmH": {
                      "type": "text",
                      "settings": {
                        "text": "<h1>{{ closest.product.title }}</h1>",
                        "width": "100%",
                        "max_width": "normal",
                        "alignment": "left",
                        "type_preset": "h3",
                        "font": "var(--font-primary--family)",
                        "font_size": "",
                        "line_height": "normal",
                        "letter_spacing": "normal",
                        "case": "none",
                        "wrap": "pretty",
                        "color": "",
                        "background": false,
                        "background_color": "#00000026",
                        "corner_radius": 0,
                        "padding-block-start": 0,
                        "padding-block-end": 0,
                        "padding-inline-start": 0,
                        "padding-inline-end": 0
                      },
                      "blocks": {}
                    }
                  },
                  "block_order": [
                    "text_j4RAyy",
                    "text_gJNTmH"
                  ]
                },
                "text_xrnftG": {
                  "type": "text",
                  "disabled": true,
                  "settings": {
                    "text": "<h1>{{ closest.product.title }}</h1>",
                    "width": "100%",
                    "max_width": "normal",
                    "alignment": "left",
                    "type_preset": "h3",
                    "font": "var(--font-primary--family)",
                    "font_size": "",
                    "line_height": "normal",
                    "letter_spacing": "normal",
                    "case": "none",
                    "wrap": "pretty",
                    "color": "",
                    "background": false,
                    "background_color": "#00000026",
                    "corner_radius": 0,
                    "padding-block-start": 0,
                    "padding-block-end": 0,
                    "padding-inline-start": 0,
                    "padding-inline-end": 0
                  },
                  "blocks": {}
                },
                "group_8B3NLh": {
                  "type": "group",
                  "name": "Price & Rating",
                  "settings": {
                    "link": "",
                    "open_in_new_tab": false,
                    "content_direction": "row",
                    "vertical_on_mobile": true,
                    "horizontal_alignment": "flex-start",
                    "vertical_alignment": "flex-start",
                    "align_baseline": false,
                    "horizontal_alignment_flex_direction_column": "flex-start",
                    "vertical_alignment_flex_direction_column": "center",
                    "gap": 24,
                    "width": "fill",
                    "custom_width": 100,
                    "width_mobile": "fill",
                    "custom_width_mobile": 100,
                    "height": "fit",
                    "custom_height": 100,
                    "inherit_color_scheme": true,
                    "color_scheme": "",
                    "background_media": "none",
                    "video_position": "cover",
                    "background_image_position": "cover",
                    "border": "none",
                    "border_width": 1,
                    "border_opacity": 100,
                    "border_radius": 0,
                    "toggle_overlay": false,
                    "overlay_color": "#00000026",
                    "overlay_style": "solid",
                    "gradient_direction": "to top",
                    "padding-block-start": 0,
                    "padding-block-end": 0,
                    "padding-inline-start": 0,
                    "padding-inline-end": 0
                  },
                  "blocks": {
                    "price_97GqNF": {
                      "type": "price",
                      "settings": {
                        "show_sale_price_first": true,
                        "show_installments": true,
                        "show_tax_info": true,
                        "type_preset": "paragraph",
                        "width": "100%",
                        "alignment": "left",
                        "font": "var(--font-body--family)",
                        "font_size": "1rem",
                        "line_height": "normal",
                        "letter_spacing": "normal",
                        "case": "none",
                        "color": "var(--color-foreground)",
                        "padding-block-start": 4,
                        "padding-block-end": 0,
                        "padding-inline-start": 0,
                        "padding-inline-end": 0
                      },
                      "blocks": {}
                    },
                    "review_QhdEwM": {
                      "type": "review",
                      "settings": {
                        "stars_style": "shaded",
                        "show_number": true,
                        "rating_color": "primary",
                        "type_preset": "paragraph",
                        "alignment": "left"
                      },
                      "blocks": {}
                    }
                  },
                  "block_order": [
                    "price_97GqNF",
                    "review_QhdEwM"
                  ]
                }
              },
              "block_order": [
                "group_Ejb3R7",
                "text_xrnftG",
                "group_8B3NLh"
              ]
            },
            "divider_MWdxWr": {
              "type": "_divider",
              "name": "t:names.divider",
              "disabled": true,
              "settings": {
                "thickness": 1,
                "corner_radius": "square",
                "width_percent": 100,
                "padding-block-start": 0,
                "padding-block-end": 0
              },
              "blocks": {}
            },
            "variant_picker": {
              "type": "variant-picker",
              "disabled": true,
              "settings": {
                "variant_style": "buttons",
                "show_swatches": true,
                "alignment": "left",
                "padding-block-start": 0,
                "padding-block-end": 0,
                "padding-inline-start": 0,
                "padding-inline-end": 0
              },
              "blocks": {}
            },
            "buy_buttons": {
              "type": "buy-buttons",
              "disabled": true,
              "settings": {
                "stacking": false,
                "show_pickup_availability": true,
                "padding-block-start": 0,
                "padding-block-end": 0,
                "padding-inline-start": 0,
                "padding-inline-end": 0
              },
              "blocks": {
                "quantity": {
                  "type": "quantity",
                  "disabled": true,
                  "static": true,
                  "settings": {},
                  "blocks": {}
                },
                "add-to-cart": {
                  "type": "add-to-cart",
                  "static": true,
                  "settings": {
                    "style_class": "button"
                  },
                  "blocks": {}
                },
                "accelerated-checkout": {
                  "type": "accelerated-checkout",
                  "static": true,
                  "settings": {},
                  "blocks": {}
                }
              },
              "block_order": []
            }
          },
          "block_order": [
            "group_JtXipy",
            "group",
            "divider_MWdxWr",
            "variant_picker",
            "buy_buttons"
          ]
        }
      },
      "settings": {
        "content_width": "content-center-aligned",
        "desktop_media_position": "left",
        "equal_columns": true,
        "limit_details_width": true,
        "gap": 48,
        "color_scheme": "scheme-1",
        "padding-block-start": 0,
        "padding-block-end": 20
      }
    },
    "product-recommendations": {
      "type": "product-recommendations",
      "blocks": {
        "header": {
          "type": "text",
          "name": "t:names.header",
          "settings": {
            "text": "<h3>You may also like</h3>",
            "width": "100%",
            "max_width": "normal",
            "alignment": "center",
            "type_preset": "h3",
            "font": "var(--font-primary--family)",
            "font_size": "",
            "line_height": "normal",
            "letter_spacing": "normal",
            "case": "none",
            "wrap": "pretty",
            "color": "",
            "background": false,
            "background_color": "#00000026",
            "corner_radius": 0,
            "padding-block-start": 0,
            "padding-block-end": 0,
            "padding-inline-start": 0,
            "padding-inline-end": 0
          },
          "blocks": {}
        },
        "static-product-card": {
          "type": "_product-card",
          "name": "t:names.product_card",
          "static": true,
          "settings": {
            "product_card_gap": 16,
            "inherit_color_scheme": true,
            "color_scheme": "",
            "border": "none",
            "border_width": 1,
            "border_opacity": 100,
            "border_radius": 0,
            "padding-block-start": 0,
            "padding-block-end": 0,
            "padding-inline-start": 0,
            "padding-inline-end": 0
          },
          "blocks": {
            "card-gallery": {
              "type": "_product-card-gallery",
              "name": "t:names.product_card_media",
              "settings": {
                "image_ratio": "square",
                "border": "none",
                "border_width": 1,
                "border_opacity": 100,
                "border_radius": 0,
                "padding-block-start": 0,
                "padding-block-end": 0,
                "padding-inline-start": 0,
                "padding-inline-end": 0
              },
              "blocks": {}
            },
            "group": {
              "type": "_product-card-group",
              "settings": {
                "link": "",
                "open_in_new_tab": false,
                "content_direction": "column",
                "vertical_on_mobile": true,
                "horizontal_alignment": "flex-start",
                "vertical_alignment": "center",
                "align_baseline": false,
                "horizontal_alignment_flex_direction_column": "center",
                "vertical_alignment_flex_direction_column": "center",
                "gap": 4,
                "width": "fill",
                "custom_width": 100,
                "width_mobile": "fill",
                "custom_width_mobile": 100,
                "height": "fit",
                "custom_height": 100,
                "inherit_color_scheme": true,
                "color_scheme": "",
                "background_media": "none",
                "video_position": "cover",
                "background_image_position": "cover",
                "border": "none",
                "border_width": 1,
                "border_opacity": 100,
                "border_radius": 0,
                "toggle_overlay": false,
                "overlay_color": "#00000026",
                "overlay_style": "solid",
                "gradient_direction": "to top",
                "padding-block-start": 0,
                "padding-block-end": 0,
                "padding-inline-start": 0,
                "padding-inline-end": 0
              },
              "blocks": {
                "product_title_AQqXJ9": {
                  "type": "product-title",
                  "name": "t:names.product_title",
                  "settings": {
                    "width": "fit-content",
                    "max_width": "normal",
                    "alignment": "left",
                    "type_preset": "h5",
                    "font": "var(--font-body--family)",
                    "font_size": "1rem",
                    "line_height": "normal",
                    "letter_spacing": "normal",
                    "case": "none",
                    "wrap": "pretty",
                    "color": "var(--color-foreground)",
                    "background": false,
                    "background_color": "#00000026",
                    "corner_radius": 0,
                    "padding-block-start": 0,
                    "padding-block-end": 0,
                    "padding-inline-start": 0,
                    "padding-inline-end": 0
                  },
                  "blocks": {}
                },
                "price": {
                  "type": "price",
                  "settings": {
                    "show_sale_price_first": true,
                    "show_installments": false,
                    "show_tax_info": false,
                    "type_preset": "paragraph",
                    "width": "100%",
                    "alignment": "center",
                    "font": "var(--font-body--family)",
                    "font_size": "1rem",
                    "line_height": "normal",
                    "letter_spacing": "normal",
                    "case": "none",
                    "color": "var(--color-foreground)",
                    "padding-block-start": 0,
                    "padding-block-end": 0,
                    "padding-inline-start": 0,
                    "padding-inline-end": 0
                  },
                  "blocks": {}
                }
              },
              "block_order": [
                "product_title_AQqXJ9",
                "price"
              ]
            }
          },
          "block_order": [
            "card-gallery",
            "group"
          ]
        }
      },
      "block_order": [
        "header"
      ],
      "settings": {
        "product": "{{ closest.product }}",
        "recommendation_type": "related",
        "layout_type": "grid",
        "carousel_on_mobile": false,
        "max_products": 4,
        "columns": 4,
        "mobile_columns": "2",
        "columns_gap": 12,
        "rows_gap": 36,
        "icons_style": "arrow",
        "icons_shape": "none",
        "section_width": "page-width",
        "gap": 28,
        "color_scheme": "scheme-1",
        "padding-block-start": 48,
        "padding-block-end": 48
      }
    },
    "how-it-works": {
      "type": "how-it-works",
      "settings": {
        "heading": "How it works",
        "description": "",
        "video_type": "youtube",
        "youtube_id": "KsHvTtTJRcw",
        "vimeo_id": "",
        "video_shopify": "",
        "video_mp4": "",
        "video_webm": "",
        "autoplay": false,
        "loop": false,
        "background_color": "#f8f9fa",
        "heading_color": "",
        "heading_size": 32,
        "heading_weight": 600,
        "video_max_width": 800,
        "video_border_radius": 12,
        "padding_top": 60,
        "padding_bottom": 60,
        "heading_margin_bottom": 40
      }
    },
    "why-upstep": {
      "type": "why-upstep",
      "blocks": {
        "benefit_1": {
          "type": "benefit",
          "settings": {
            "icon_type": "preset",
            "preset_icon": "price",
            "custom_svg_code": "",
            "title": "Affordable price",
            "description": "Upstep prices are up to 60% less than any other foot specialist."
          }
        },
        "benefit_2": {
          "type": "benefit",
          "settings": {
            "icon_type": "preset",
            "preset_icon": "guarantee",
            "custom_svg_code": "",
            "title": "Satisfaction guarantee",
            "description": "A 180 day money back guarantee and remakes with NO catches."
          }
        },
        "benefit_3": {
          "type": "benefit",
          "settings": {
            "icon_type": "preset",
            "preset_icon": "process",
            "custom_svg_code": "",
            "title": "Quick Process",
            "description": "100% from home, start to finish the process couldn't be simpler."
          }
        },
        "benefit_4": {
          "type": "benefit",
          "settings": {
            "icon_type": "preset",
            "preset_icon": "doctor",
            "custom_svg_code": "",
            "title": "Podiatrist designed",
            "description": "Years of podiatric knowledge and experience go into every orthotics pair."
          }
        }
      },
      "block_order": [
        "benefit_1",
        "benefit_2",
        "benefit_3",
        "benefit_4"
      ],
      "settings": {
        "heading": "Why Upstep?",
        "description": "<p>We will use top-quality materials for your needs and produce your custom orthotics with the most advanced manufacturing methods!</p><p>Then we will send your custom orthotics via FedEx to your doorstep.</p>",
        "button_text": "TAKE THE QUIZ",
        "button_url": "",
        "button_open_new_tab": false,
        "button_subtext": "180-day money-back guarantee",
        "background_color": "#ffffff",
        "button_background": "#0066ff",
        "button_color": "#ffffff",
        "padding_top": 60,
        "padding_bottom": 60
      }
    }
  },
  "order": [
    "17533432653b3c2f79",
    "main",
    "product-recommendations",
    "how-it-works",
    "why-upstep"
  ]
}

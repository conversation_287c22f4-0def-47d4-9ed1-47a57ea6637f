:host {
  display: grid;
  width: 100%;
}

[part='list'] {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: var(--overflow-list-alignment);
  column-gap: 1rem;

  @media (max-width: 749px) {
    justify-content: var(--overflow-list-alignment-mobile);
  }
}

[part='list'],
[part='overflow-list'],
[part='placeholder'] {
  margin: 0;
  padding: 0;
  list-style: none;
}

/* Make sure the "more" slot can be measured */
slot[name='more']:not([hidden]) {
  display: block;
}

slot[name='more'] .button {
  cursor: pointer;
  border: none;
  background: none;
  padding: 0;
  margin: 0;
  font-family: var(--font-paragraph-family);
  font-size: var(--font-paragraph-size);
  text-transform: var(--text-transform);
  color: currentcolor;
  text-align: start;
}

[part='overflow'] {
  display: none;
}

[part='placeholder'] {
  visibility: hidden;
  width: 0;
  height: 0;
}

:host([disabled]) {
  slot[name='more'] {
    display: none;
  }
}

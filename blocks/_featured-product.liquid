

{% capture product_card_children %}
  <div class="featured-product-content-top">
    {% content_for 'block', type: 'product-title', id: 'featured-product-title' %}

    {% content_for 'block', type: '_featured-product-price', id: 'featured-product-price' %}
  </div>
  {% content_for 'block', type: '_featured-product-gallery', id: 'featured-product-gallery' %}
  <div class="featured-product-content-bottom">
    {%- content_for 'block', type: 'swatches', id: 'featured-product-swatches' -%}
  </div>
{% endcapture %}

{% render 'product-card', children: product_card_children, product: closest.product, product_card_gap: 8 %}

{% stylesheet %}
  .featured-product-content-top {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    gap: var(--gap-sm);
  }

  .featured-product-content-bottom:not(:has(product-swatches)) {
    display: none;
  }
{% endstylesheet %}

{% schema %}
{
  "name": "t:names.product",
  "tag": null,
  "settings": [
    {
      "type": "paragraph",
      "content": "t:content.resource_reference_product_card"
    }
  ]
}
{% endschema %}


{%- doc -%}
  Renders a header menu block.

  @param {string} [variant] - What version of the menu to render. Defaults to header menu.
  @param {string} [transparent] - Show a transparent menu
{%- enddoc -%}

{% liquid
  assign animation_speed = 200

  if block.settings.menu_style == 'featured_products' and block.settings.product_title_case == 'uppercase'
    assign title_case_style = ' --title-case: uppercase;'
  elsif block.settings.menu_style == 'featured_collections' and block.settings.collection_title_case == 'uppercase'
    assign title_case_style = ' --title-case: uppercase;'
  elsif block.settings.menu_style == 'collection_images' and block.settings.collection_title_case == 'uppercase'
    assign title_case_style = ' --title-case: uppercase;'
  endif
%}

{% case variant %}
  {% when 'mobile' %}
    <div
      class="header__drawer desktop:hidden"
      ref="headerDrawerContainer"
      style="{{ title_case_style }}"
      {{ block.shopify_attributes }}
    >
      {% render 'header-drawer',
        class: 'header__drawer--mobile',
        linklist: block.settings.menu,
        data_header_drawer_type: 'mobile-drawer'
      %}
    </div>

  {% when 'navigation_bar' %}
    {% if block.settings.navigation_bar == true %}
      <div
        class="menu-list menu-list--mobile{% if transparent == blank %} color-{{ block.settings.color_scheme_navigation_bar }}{% endif %}"
        style="--menu-horizontal-gap: 15px; --mobile-nav-margin: auto;{{ title_case_style }}"
      >
        <div class="menu-list__scroll-container">
          <ul
            class="menu-list__list list-unstyled"
            role="list"
          >
            {% for link in block.settings.menu.links %}
              <li>
                <a
                  href="{{ link.url }}"
                  id="MenuItem-{{ link.handle }}"
                  class="menu-list__item"
                  {% if link.current %}
                    aria-current="page"
                  {% endif %}
                >
                  {{- link.title -}}
                </a>
              </li>
            {% endfor %}
          </ul>
        </div>
      </div>
    {% endif %}
  {% else %}
    <header-menu
      ref="headerMenu"
      class="header-menu mobile:hidden"
      data-animation-delay="{{ animation_speed }}"
      {{ block.shopify_attributes }}
      style="--submenu-animation-speed: {{ animation_speed }}ms;{{ title_case_style }}"
    >
      <div class="header-menu__inner">
        {%- render 'header-menu' %}
      </div>

      <script
        src="{{ 'header-menu.js' | asset_url }}"
        type="module"
      ></script>
    </header-menu>
{% endcase %}

{% stylesheet %}
  .menu-list--mobile {
    &.menu-list {
      display: grid;
    }

    & .menu-list__list {
      width: max-content;
      margin-inline: auto;
      gap: var(--menu-horizontal-gap);
    }

    & li {
      width: max-content;
      padding-block: var(--padding-sm);
    }

    & li:first-of-type {
      padding-inline-start: var(--menu-horizontal-gap);
    }

    & li:last-of-type {
      padding-inline-end: var(--menu-horizontal-gap);
    }

    & a {
      color: var(--color-foreground);
    }
  }

  .menu-list__scroll-container {
    position: relative;
    overflow-x: auto;
    mask-image: linear-gradient(to right, transparent, black 20px, black calc(100% - 20px), transparent);
    padding-block: var(--padding-2xs);
  }

  header-menu {
    width: 100%;
  }

  /* Drawer menu featured content */
  .menu-drawer__featured-content {
    z-index: var(--layer-lowest);
    container-type: inline-size;
  }

  .menu-drawer__featured-content--childlist {
    z-index: var(--layer-flat);
  }

  .menu-drawer__featured-content-list {
    display: flex;
    gap: 1em;
    overflow-x: auto;
    padding-block-end: var(--padding-lg);
  }

  .menu-drawer__featured-content-list-item {
    flex: 0 0 auto;
  }

  .menu-drawer__featured-content-list-item--product {
    width: 35cqi;
  }

  .menu-drawer__featured-content-list-item--collection img.resource-card__image {
    width: 80cqi;
  }

  .menu-drawer__featured-content-list-item:first-child {
    margin-inline-start: var(--margin-xl);
  }

  .menu-drawer__featured-content-list-item:last-child {
    margin-inline-end: var(--margin-xl);
  }

  /* Apply title case to drawer menu featured content */
  .menu-drawer__featured-content-list-item--product .resource-card__title,
  .menu-drawer__featured-content-list-item--collection .resource-card__title {
    text-transform: var(--title-case);
  }

  .menu-list {
    --menu-horizontal-gap: var(--gap-xl);
    --menu-vertical-gap: var(--gap-xl);

    display: flex;
  }

  .menu-list__list {
    display: flex;
    justify-content: var(--grid-area-alignment);
  }

  .menu-list__list-item {
    flex-shrink: 0;
    white-space: nowrap;
  }

  .menu-list__list-item[aria-hidden='true'] {
    visibility: hidden;
  }

  .menu-list__link {
    font-family: var(--menu-top-level-font-family);
    font-style: var(--menu-top-level-font-style);
    font-weight: var(--menu-top-level-font-weight);
    font-size: var(--menu-top-level-font-size);
    line-height: var(--menu-top-level-font-line-height);
    text-transform: var(--menu-top-level-font-case);
    color: var(--menu-top-level-font-color);
    transition: color var(--animation-speed) var(--animation-easing);
    text-decoration: none;
    display: flex;
    min-height: var(--minimum-touch-target);
    align-items: center;

    &:hover,
    &:focus {
      color: var(--menu-top-level-font-color);
    }

    @media screen and (min-width: 750px) {
      font-size: var(--menu-top-level-font-size-desktop);
    }
  }

  /*
      High specificity selectors to subdue non-hovered links without javascript.
      If the need for js-generated `hovered` and `focused` classes arises for another reason we can simplify these.
    */
  .menu-list:where(:has(.menu-list__list-item:hover)),
  .menu-list:where(:has(.menu-list__list-item:focus-within)),
  .menu-list:where(:has(.menu-list__list-item:not([aria-hidden='true']) .menu-list__link--active)) {
    .menu-list__link {
      color: rgb(from var(--menu-top-level-font-color) r g b / var(--opacity-subdued-text));
    }
  }

  /* stylelint-disable-next-line selector-max-specificity */
  .menu-list:not(:has(.menu-list__list-item:hover)) .menu-list__link--active,
  .menu-list .menu-list__list-item:where(:hover, :focus-within) .menu-list__link,
  .menu-list .menu-list__list-item[slot='overflow'] .menu-list__link[aria-expanded='true'] {
    color: var(--menu-top-level-font-color);
  }

  .overflow-menu::part(list) {
    gap: 0 var(--menu-horizontal-gap);

    /* Make sure focus outline is not cut off by overflow hidden */
    --focus-outline-size: calc(var(--focus-outline-offset) + var(--focus-outline-width));
  }

  .overflow-menu {
    background-color: transparent;
    padding: var(--focus-outline-size);
    margin: calc(-1 * var(--focus-outline-size));
  }

  /* the submenu background element to animate */
  .overflow-menu::after {
    content: '';
    position: absolute;
    top: calc(100% - 1px + var(--border-bottom-width));
    left: 0;
    width: 100%;
    height: var(--submenu-height);
    background-color: var(--color-background);
    box-shadow: var(--shadow-popover);
    clip-path: inset(0.5px 0 -100px 0);
    transition: height var(--submenu-animation-speed) var(--ease-out-cubic);
    z-index: calc(var(--layer-header-menu) - 1);
  }

  /** mega menu **/
  .menu-list__submenu,
  .overflow-menu::part(overflow) {
    --submenu-padding-block-start: var(--padding-3xl);
    --submenu-padding-block-end: var(--padding-3xl);

    background-color: transparent;
  }

  .header__row[style*='--border-bottom-width: 0px'] {
    .menu-list__submenu.color-scheme-matches-parent,
    .overflow-menu.color-scheme-matches-parent::part(overflow) {
      --submenu-padding-block-start: 0px;
    }
  }

  .menu-list__list-item:where(:not([slot='overflow'])) > .menu-list__submenu,
  .overflow-menu::part(overflow) {
    --submenu-content-opacity: 0;
    --submenu-content-animation: opacity calc(var(--submenu-animation-speed) * 0.75) var(--animation-easing);

    box-shadow: var(--shadow-drawer);
    visibility: hidden;
    background-color: transparent;
    position: absolute;
    width: 100%;
    left: 0;
    top: calc(100% - 1px + var(--border-bottom-width));
    z-index: var(--layer-header-menu);
    padding-inline: var(--padding-inline);
    clip-path: rect(0 100% var(--submenu-height) 0);
    transition: clip-path var(--submenu-animation-speed) var(--ease-out-cubic);
  }

  /* Show the submenus on hover */
  .menu-list__list-item:has([aria-expanded='true']) > .menu-list__submenu,
  /* Show the overflow menu when a menu item is hovered */
  .overflow-menu:has([slot="overflow"] [aria-expanded='true'])::part(overflow),
  /* Keep the submenus open when they are hovered */
  .menu-list__submenu:is(:hover),
  .overflow-menu::part(overflow):hover {
    visibility: visible;
    --submenu-content-opacity: 1;
  }

  .menu-list__list-item:has([data-animating]) > .menu-list__submenu {
    visibility: visible;
    --submenu-content-opacity: 0;

    &:before {
      height: 0;
    }
  }

  .menu-list__submenu-inner,
  .overflow-menu::part(overflow-list) {
    grid-column: 2;
    padding-block-start: var(--submenu-padding-block-start);
    padding-block-end: var(--submenu-padding-block-end);
    padding-inline: var(--section-padding-inline);
  }

  .overflow-menu::part(overflow) {
    --menu-top-level-font-size: var(--font-size--xlarge);
    display: grid;
    grid-template-columns: var(--full-page-grid-with-margins);
  }

  .overflow-menu::part(overflow-list) {
    --submenu-content-opacity: 0;

    position: relative;
    display: grid;
    grid-template-columns: minmax(auto, 200px) 1fr;
    grid-template-areas: 'left right';
    grid-template-rows: max-content;
    grid-gap: 0;
  }

  .menu-list__list-item:is([slot='overflow']) {
    --menu-top-level-font-color: var(--color-foreground);

    display: contents;
    white-space: normal;

    .menu-list__link {
      --submenu-content-opacity: 0;

      grid-area: left;
      grid-row: auto;
      height: min-content;
      font-size: var(--font-size--xl);
      opacity: var(--submenu-content-opacity);
      transition: var(--submenu-content-animation);
    }

    .menu-list__submenu {
      visibility: hidden;
      grid-row: 1;
      grid-area: right;
      grid-row-end: span calc(var(--overflow-count) + 1);
      padding-inline-start: var(--menu-horizontal-gap);

      .menu-list__submenu-inner {
        padding-block-start: 0;
        grid-column: unset;
      }
    }

    .menu-list__link[aria-expanded='true'] + .menu-list__submenu {
      visibility: visible;
    }
  }

  .header-menu[data-overflow-expanded='true'] .menu-list__list-item:where([slot='overflow']) .menu-list__link {
    --submenu-content-opacity: 1;
  }

  .menu-list__list-item:where([slot='overflow']):has([data-animating]) > .menu-list__link {
    --submenu-content-opacity: 0;
  }

  .menu-list__submenu-inner {
    position: relative;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    opacity: var(--submenu-content-opacity);
    transition: var(--submenu-content-animation);
  }

  .mega-menu__link {
    font-family: var(--menu-child-font-family);
    font-style: var(--menu-child-font-style);
    font-weight: var(--menu-child-font-weight);
    font-size: var(--menu-child-font-size);
    line-height: var(--menu-child-font-line-height);
    text-transform: var(--menu-child-font-case);
    color: var(--menu-child-font-color);
    white-space: normal;
    text-decoration: none;
    display: inline-flex;
    padding: var(--padding-2xs) 0;
    transition: color var(--animation-speed) var(--animation-easing);

    &:hover {
      color: var(--menu-child-active-font-color);
    }
  }

  .mega-menu__link--parent {
    font-family: var(--menu-parent-font-family);
    font-style: var(--menu-parent-font-style);
    font-weight: var(--menu-parent-font-weight);
    font-size: var(--menu-parent-font-size);
    line-height: var(--menu-parent-font-line-height);
    text-transform: var(--menu-parent-font-case);
    color: var(--menu-parent-font-color);

    &:hover {
      color: var(--menu-parent-active-font-color);
    }
  }

  @media screen and (max-width: 989px) {
    .mega-menu__content-list-item--hidden-tablet {
      display: none;
    }
  }

  .mega-menu__link:has(.mega-menu__link-image) {
    display: flex;
    flex-direction: column;
    padding-inline: 0;
    padding-block: var(--padding-sm) 0;
  }

  .mega-menu__link-image {
    width: 100%;
    position: relative;
    aspect-ratio: 16 / 9;
    margin-bottom: var(--padding-sm);
    object-fit: cover;
    border-radius: var(--menu-image-border-radius);
  }

  .mega-menu__grid {
    display: grid;
    grid-template-columns: repeat(var(--menu-columns-tablet), minmax(0, 1fr));
    gap: var(--menu-vertical-gap) var(--menu-horizontal-gap);
    width: 100%;

    @media screen and (min-width: 990px) {
      grid-template-columns: repeat(var(--menu-columns-desktop), minmax(0, 1fr));
    }
  }

  .mega-menu__column {
    grid-column: span 1;
  }

  .mega-menu__column--span-2 {
    grid-column: span 2;
  }

  .mega-menu__column--span-3 {
    grid-column: span 3;
  }

  .mega-menu__column--span-4 {
    grid-column: span 4;
  }

  .mega-menu__column--collection-image {
    grid-column: span 1;

    @media screen and (min-width: 990px) {
      grid-column: span 2;
    }
  }

  .mega-menu__column--collection-image .mega-menu__link {
    text-transform: var(--title-case);
  }

  .mega-menu__submenu .mega-menu__column--collection-image {
    grid-column: span 1;
  }

  /* Apply title case to featured products and collections */
  .mega-menu__content-list--products .resource-card__title,
  .mega-menu__content-list--collections .resource-card__title {
    text-transform: var(--title-case);
  }

  .mega-menu__content-list {
    display: grid;
    justify-content: end;
    gap: var(--menu-vertical-gap) var(--menu-horizontal-gap);
  }

  .mega-menu__content-list--products {
    grid-template-columns: repeat(var(--menu-content-columns-tablet), minmax(0, 1fr));

    @media screen and (min-width: 990px) {
      grid-template-columns: repeat(var(--menu-content-columns-desktop), minmax(0, 1fr));
    }
  }

  .mega-menu__content-list--collections {
    grid-template-columns: repeat(var(--menu-content-columns-tablet), minmax(0, 300px));

    @media screen and (min-width: 990px) {
      grid-template-columns: repeat(var(--menu-content-columns-desktop), minmax(0, 300px));
    }
  }

  .mega-menu__list {
    display: grid;
    grid-template-columns: subgrid;
    grid-column: span var(--menu-columns-tablet);
    gap: var(--menu-vertical-gap) var(--menu-horizontal-gap);

    @media screen and (min-width: 990px) {
      grid-column: span var(--menu-columns-desktop);
    }
  }

  .mega-menu__content {
    grid-column: span var(--menu-content-columns-tablet) / -1;

    @media screen and (min-width: 990px) {
      grid-column: span var(--menu-content-columns-desktop) / -1;
    }
  }

  .menu-list__list-item[slot='overflow'] .section {
    grid-template-columns: 1fr;
  }

  .menu-list__list-item[slot='overflow'] .section .mega-menu__grid {
    grid-column: 1;
  }

  .mega-menu__content-list li {
    white-space: normal;
  }

  /* mega more menu */
  .mega-menu__more-list {
    --menu-child-font-size: var(--font-size--xl);

    width: 200px;
  }

  .mega-menu__more-list-item .mega-menu__link {
    color: rgb(from var(--menu-top-level-font-color) r g b / var(--opacity-subdued-text));
  }

  .mega-menu__more-list-item:where(:hover, .active) .mega-menu__link {
    color: var(--menu-top-level-font-color);
  }

  .mega-menu__more-list-item[aria-hidden='true'] {
    display: none;
  }

  .mega-menu__submenu {
    /* preserves the inherited grid layout when this submenu wrapper is used */
    display: contents;
  }
{% endstylesheet %}

{% schema %}
{
  "name": "t:names.menu",
  "tag": null,
  "settings": [
    {
      "type": "link_list",
      "id": "menu",
      "label": "t:settings.menu",
      "default": "main-menu"
    },
    {
      "type": "select",
      "id": "menu_style",
      "label": "t:settings.style",
      "options": [
        {
          "value": "text",
          "label": "t:options.text_only"
        },
        {
          "value": "collection_images",
          "label": "t:options.collection_images"
        },
        {
          "value": "featured_products",
          "label": "t:options.featured_products"
        },
        {
          "value": "featured_collections",
          "label": "t:options.featured_collections"
        }
      ],
      "default": "collection_images"
    },
    {
      "type": "select",
      "id": "featured_products_aspect_ratio",
      "label": "t:settings.image_ratio",
      "options": [
        {
          "value": "adapt",
          "label": "t:options.auto"
        },
        {
          "value": "4 / 5",
          "label": "t:options.portrait"
        },
        {
          "value": "1 / 1",
          "label": "t:options.square"
        }
      ],
      "default": "4 / 5",
      "visible_if": "{{ block.settings.menu_style == 'featured_products' }}"
    },
    {
      "type": "select",
      "id": "featured_collections_aspect_ratio",
      "label": "t:settings.image_ratio",
      "options": [
        {
          "value": "adapt",
          "label": "t:options.auto"
        },
        {
          "value": "16 / 9",
          "label": "t:options.landscape"
        },
        {
          "value": "1 / 1",
          "label": "t:options.square"
        }
      ],
      "default": "16 / 9",
      "visible_if": "{{ block.settings.menu_style == 'featured_collections' }}"
    },
    {
      "type": "range",
      "id": "image_border_radius",
      "label": "t:settings.image_border_radius",
      "min": 0,
      "max": 32,
      "step": 1,
      "unit": "px",
      "default": 0,
      "visible_if": "{{ block.settings.menu_style != 'text' }}"
    },
    {
      "type": "select",
      "id": "product_title_case",
      "label": "t:settings.product_title_case",
      "options": [
        {
          "value": "default",
          "label": "t:options.default"
        },
        {
          "value": "uppercase",
          "label": "t:options.uppercase"
        }
      ],
      "default": "default",
      "visible_if": "{{ block.settings.menu_style == 'featured_products' }}"
    },
    {
      "type": "select",
      "id": "collection_title_case",
      "label": "t:settings.collection_title_case",
      "options": [
        {
          "value": "default",
          "label": "t:options.default"
        },
        {
          "value": "uppercase",
          "label": "t:options.uppercase"
        }
      ],
      "default": "default",
      "visible_if": "{{ block.settings.menu_style == 'featured_collections' or block.settings.menu_style == 'collection_images' }}"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:settings.color_scheme",
      "default": "primary"
    },
    {
      "type": "select",
      "id": "menu_font_style",
      "label": "t:settings.text_hierarchy",
      "options": [
        {
          "value": "regular",
          "label": "t:options.classic"
        },
        {
          "value": "inverse",
          "label": "t:options.inverse"
        },
        {
          "value": "inverse_large",
          "label": "t:options.inverse_large"
        }
      ],
      "default": "inverse"
    },
    {
      "type": "header",
      "content": "t:content.typography_primary"
    },
    {
      "type": "select",
      "id": "type_font_primary_link",
      "label": "t:settings.font",
      "options": [
        {
          "value": "body",
          "label": "t:options.body"
        },
        {
          "value": "subheading",
          "label": "t:options.subheading"
        },
        {
          "value": "heading",
          "label": "t:options.heading"
        },
        {
          "value": "accent",
          "label": "t:options.accent"
        }
      ],
      "default": "heading"
    },
    {
      "type": "select",
      "id": "type_font_primary_size",
      "label": "t:settings.size",
      "options": [
        {
          "value": "0.625rem",
          "label": "10px"
        },
        {
          "value": "0.75rem",
          "label": "12px"
        },
        {
          "value": "0.875rem",
          "label": "14px"
        },
        {
          "value": "1rem",
          "label": "16px"
        },
        {
          "value": "1.125rem",
          "label": "18px"
        },
        {
          "value": "1.25rem",
          "label": "20px"
        },
        {
          "value": "1.5rem",
          "label": "24px"
        },
        {
          "value": "2rem",
          "label": "32px"
        },
        {
          "value": "2.5rem",
          "label": "40px"
        },
        {
          "value": "3rem",
          "label": "48px"
        },
        {
          "value": "3.5rem",
          "label": "56px"
        },
        {
          "value": "4.5rem",
          "label": "72px"
        },
        {
          "value": "5.5rem",
          "label": "88px"
        },
        {
          "value": "7.5rem",
          "label": "120px"
        },
        {
          "value": "9.5rem",
          "label": "152px"
        },
        {
          "value": "11.5rem",
          "label": "184px"
        }
      ],
      "default": "1rem"
    },
    {
      "type": "select",
      "id": "type_case_primary_link",
      "label": "t:settings.text_case",
      "options": [
        {
          "value": "none",
          "label": "t:options.default"
        },
        {
          "value": "uppercase",
          "label": "t:options.uppercase"
        }
      ],
      "default": "none"
    },
    {
      "type": "header",
      "content": "t:content.typography_secondary"
    },
    {
      "type": "select",
      "id": "type_font_secondary_link",
      "label": "t:settings.font",
      "options": [
        {
          "value": "body",
          "label": "t:options.body"
        },
        {
          "value": "subheading",
          "label": "t:options.subheading"
        },
        {
          "value": "heading",
          "label": "t:options.heading"
        },
        {
          "value": "accent",
          "label": "t:options.accent"
        }
      ],
      "default": "subheading"
    },
    {
      "type": "select",
      "id": "type_case_secondary_link",
      "label": "t:settings.text_case",
      "options": [
        {
          "value": "none",
          "label": "t:options.default"
        },
        {
          "value": "uppercase",
          "label": "t:options.uppercase"
        }
      ],
      "default": "none"
    },
    {
      "type": "header",
      "content": "t:content.typography_tertiary"
    },
    {
      "type": "select",
      "id": "type_font_tertiary_link",
      "label": "t:settings.font",
      "options": [
        {
          "value": "body",
          "label": "t:options.body"
        },
        {
          "value": "subheading",
          "label": "t:options.subheading"
        },
        {
          "value": "heading",
          "label": "t:options.heading"
        },
        {
          "value": "accent",
          "label": "t:options.accent"
        }
      ],
      "default": "body"
    },
    {
      "type": "select",
      "id": "type_case_tertiary_link",
      "label": "t:settings.text_case",
      "options": [
        {
          "value": "none",
          "label": "t:options.default"
        },
        {
          "value": "uppercase",
          "label": "t:options.uppercase"
        }
      ],
      "default": "none"
    },
    {
      "type": "header",
      "content": "t:content.mobile_layout"
    },
    {
      "type": "checkbox",
      "id": "navigation_bar",
      "label": "t:settings.navigation_bar"
    },
    {
      "type": "color_scheme",
      "id": "color_scheme_navigation_bar",
      "label": "t:settings.navigation_bar_color_scheme",
      "default": "primary",
      "visible_if": "{{ block.settings.navigation_bar }}"
    },
    {
      "type": "checkbox",
      "id": "drawer_accordion",
      "label": "t:settings.accordion",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "drawer_accordion_expand_first",
      "label": "t:settings.expand_first_group",
      "default": false,
      "visible_if": "{{ block.settings.drawer_accordion == true }}"
    },
    {
      "type": "checkbox",
      "id": "drawer_dividers",
      "label": "t:settings.dividers",
      "default": false
    }
  ]
}
{% endschema %}

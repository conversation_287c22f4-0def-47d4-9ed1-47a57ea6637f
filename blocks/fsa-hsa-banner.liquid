{% comment %}
  FSA/HSA Banner Block - Blue banner for FSA/HSA information
  Designed for product pages to highlight FSA/HSA eligibility
{% endcomment %}

<div
  class="fsa-hsa-banner"
  style="
    --banner-background: {{ block.settings.background_color | default: 'linear-gradient(135deg, #1e3a8a 0%, #3b82f6 100%)' }};
    --banner-text-color: {{ block.settings.text_color | default: 'white' }};
    --banner-border-radius: {{ block.settings.border_radius | default: 12 }}px;
    --banner-padding-vertical: {{ block.settings.padding_vertical | default: 20 }}px;
    --banner-padding-horizontal: {{ block.settings.padding_horizontal | default: 24 }}px;
    --banner-margin-top: {{ block.settings.margin_top | default: 0 }}px;
    --banner-margin-bottom: {{ block.settings.margin_bottom | default: 24 }}px;
    --banner-title-size: {{ block.settings.title_size | default: 20 }}px;
    --banner-title-weight: {{ block.settings.title_weight | default: 600 }};
    --banner-link-size: {{ block.settings.link_size | default: 15 }}px;
    --banner-link-weight: {{ block.settings.link_weight | default: 500 }};
    --banner-shadow: {{ block.settings.box_shadow | default: '0 4px 12px rgba(0, 0, 0, 0.1)' }};
    background: var(--banner-background);
    color: var(--banner-text-color);
    border-radius: var(--banner-border-radius);
    padding: var(--banner-padding-vertical) var(--banner-padding-horizontal);
    margin-top: var(--banner-margin-top);
    margin-bottom: var(--banner-margin-bottom);
    box-shadow: var(--banner-shadow);
  "
  {{ block.shopify_attributes }}
>
  <div class="fsa-hsa-banner__content">
    {% if block.settings.title != blank %}
      <h3 class="fsa-hsa-banner__title">{{ block.settings.title }}</h3>
    {% endif %}
    
    {% if block.settings.link_url != blank and block.settings.link_text != blank %}
      <a href="{{ block.settings.link_url }}" class="fsa-hsa-banner__link">
        {% if block.settings.show_icon %}
          <svg class="fsa-hsa-banner__icon" width="16" height="16" viewBox="0 0 16 16" fill="none">
            <circle cx="8" cy="8" r="7" stroke="currentColor" stroke-width="2"/>
            <path d="M8 4v4l3 3" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
          </svg>
        {% endif %}
        {{ block.settings.link_text }}
      </a>
    {% endif %}
  </div>
</div>

{% stylesheet %}
  .fsa-hsa-banner {
    text-align: center;
    display: block;
    width: 100%;
  }

  .fsa-hsa-banner__content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
  }

  .fsa-hsa-banner__title {
    font-size: var(--banner-title-size);
    font-weight: var(--banner-title-weight);
    margin: 0;
    line-height: 1.3;
    color: inherit;
  }

  .fsa-hsa-banner__link {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    color: inherit;
    text-decoration: none;
    font-size: var(--banner-link-size);
    font-weight: var(--banner-link-weight);
    border-bottom: 1px solid rgba(255, 255, 255, 0.3);
    padding-bottom: 2px;
    transition: border-color 0.2s ease;
  }

  .fsa-hsa-banner__link:hover {
    border-bottom-color: currentColor;
  }

  .fsa-hsa-banner__icon {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
  }

  /* Mobile responsiveness */
  @media screen and (max-width: 768px) {
    .fsa-hsa-banner {
      padding: var(--banner-padding-vertical-mobile, 18px) var(--banner-padding-horizontal-mobile, 20px);
      border-radius: var(--banner-border-radius-mobile, 10px);
      margin-bottom: var(--banner-margin-bottom-mobile, 20px);
    }

    .fsa-hsa-banner__title {
      font-size: var(--banner-title-size-mobile, 18px);
    }

    .fsa-hsa-banner__link {
      font-size: var(--banner-link-size-mobile, 14px);
    }
  }

  @media screen and (max-width: 480px) {
    .fsa-hsa-banner {
      padding: var(--banner-padding-vertical-small, 16px) var(--banner-padding-horizontal-small, 16px);
      border-radius: var(--banner-border-radius-small, 8px);
    }

    .fsa-hsa-banner__title {
      font-size: var(--banner-title-size-small, 16px);
    }

    .fsa-hsa-banner__link {
      font-size: var(--banner-link-size-small, 13px);
    }

    .fsa-hsa-banner__content {
      gap: 10px;
    }
  }
{% endstylesheet %}

{% schema %}
{
  "name": "FSA/HSA Banner",
  "settings": [
    {
      "type": "header",
      "content": "Content"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Banner title",
      "default": "Use Your FSA/HSA Dollars Before You Lose Them"
    },
    {
      "type": "url",
      "id": "link_url",
      "label": "Link URL"
    },
    {
      "type": "text",
      "id": "link_text",
      "label": "Link text",
      "default": "Learn more"
    },
    {
      "type": "checkbox",
      "id": "show_icon",
      "label": "Show clock icon",
      "default": true
    },
    {
      "type": "header",
      "content": "Styling"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background color"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color"
    },
    {
      "type": "text",
      "id": "box_shadow",
      "label": "Box shadow",
      "default": "0 4px 12px rgba(0, 0, 0, 0.1)",
      "info": "CSS box-shadow value"
    },
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "range",
      "id": "border_radius",
      "label": "Border radius",
      "min": 0,
      "max": 20,
      "step": 1,
      "unit": "px",
      "default": 12
    },
    {
      "type": "range",
      "id": "padding_vertical",
      "label": "Vertical padding",
      "min": 8,
      "max": 40,
      "step": 2,
      "unit": "px",
      "default": 20
    },
    {
      "type": "range",
      "id": "padding_horizontal",
      "label": "Horizontal padding",
      "min": 8,
      "max": 40,
      "step": 2,
      "unit": "px",
      "default": 24
    },
    {
      "type": "range",
      "id": "margin_top",
      "label": "Top margin",
      "min": 0,
      "max": 60,
      "step": 4,
      "unit": "px",
      "default": 0
    },
    {
      "type": "range",
      "id": "margin_bottom",
      "label": "Bottom margin",
      "min": 0,
      "max": 60,
      "step": 4,
      "unit": "px",
      "default": 24
    },
    {
      "type": "header",
      "content": "Typography"
    },
    {
      "type": "range",
      "id": "title_size",
      "label": "Title size",
      "min": 14,
      "max": 28,
      "step": 1,
      "unit": "px",
      "default": 20
    },
    {
      "type": "range",
      "id": "title_weight",
      "label": "Title weight",
      "min": 300,
      "max": 800,
      "step": 100,
      "default": 600
    },
    {
      "type": "range",
      "id": "link_size",
      "label": "Link size",
      "min": 12,
      "max": 20,
      "step": 1,
      "unit": "px",
      "default": 15
    },
    {
      "type": "range",
      "id": "link_weight",
      "label": "Link weight",
      "min": 300,
      "max": 800,
      "step": 100,
      "default": 500
    }
  ]
}
{% endschema %}

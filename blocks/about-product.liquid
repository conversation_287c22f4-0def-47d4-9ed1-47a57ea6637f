{% comment %}
  About Product Block - Text section for product description
  Designed for product pages to provide detailed product information
{% endcomment %}

<div
  class="about-product"
  style="
    --about-background: {{ block.settings.background_color | default: 'transparent' }};
    --about-border-radius: {{ block.settings.border_radius | default: 0 }}px;
    --about-padding-vertical: {{ block.settings.padding_vertical | default: 0 }}px;
    --about-padding-horizontal: {{ block.settings.padding_horizontal | default: 0 }}px;
    --about-margin-top: {{ block.settings.margin_top | default: 0 }}px;
    --about-margin-bottom: {{ block.settings.margin_bottom | default: 24 }}px;
    --about-heading-size: {{ block.settings.heading_size | default: 18 }}px;
    --about-heading-weight: {{ block.settings.heading_weight | default: 600 }};
    --about-heading-color: {{ block.settings.heading_color | default: 'rgba(var(--color-foreground), 1)' }};
    --about-heading-margin-bottom: {{ block.settings.heading_margin_bottom | default: 12 }}px;
    --about-text-size: {{ block.settings.text_size | default: 15 }}px;
    --about-text-weight: {{ block.settings.text_weight | default: 400 }};
    --about-text-color: {{ block.settings.text_color | default: 'rgba(var(--color-foreground), 0.8)' }};
    --about-text-line-height: {{ block.settings.text_line_height | default: 1.6 }};
    --about-border-bottom-width: {{ block.settings.border_bottom_width | default: 1 }}px;
    --about-border-bottom-color: {{ block.settings.border_bottom_color | default: 'rgba(var(--color-foreground), 0.1)' }};
    --about-padding-bottom: {{ block.settings.padding_bottom | default: 24 }}px;
    background: var(--about-background);
    border-radius: var(--about-border-radius);
    padding: var(--about-padding-vertical) var(--about-padding-horizontal);
    padding-bottom: var(--about-padding-bottom);
    margin-top: var(--about-margin-top);
    margin-bottom: var(--about-margin-bottom);
    border-bottom: var(--about-border-bottom-width) solid var(--about-border-bottom-color);
  "
  {{ block.shopify_attributes }}
>
  {% if block.settings.heading != blank %}
    <h2 class="about-product__heading">{{ block.settings.heading }}</h2>
  {% endif %}
  
  {% if block.settings.description != blank %}
    <div class="about-product__description">{{ block.settings.description }}</div>
  {% endif %}
</div>

{% stylesheet %}
  .about-product {
    text-align: left;
    width: 100%;
  }

  .about-product__heading {
    font-size: var(--about-heading-size);
    font-weight: var(--about-heading-weight);
    color: var(--about-heading-color);
    margin: 0 0 var(--about-heading-margin-bottom) 0;
    line-height: 1.3;
  }

  .about-product__description {
    font-size: var(--about-text-size);
    font-weight: var(--about-text-weight);
    color: var(--about-text-color);
    line-height: var(--about-text-line-height);
    margin: 0;
  }

  /* Mobile responsiveness */
  @media screen and (max-width: 768px) {
    .about-product {
      padding: var(--about-padding-vertical-mobile, 0) var(--about-padding-horizontal-mobile, 0);
      padding-bottom: var(--about-padding-bottom-mobile, 20px);
      margin-bottom: var(--about-margin-bottom-mobile, 20px);
    }

    .about-product__heading {
      font-size: var(--about-heading-size-mobile, 16px);
      margin-bottom: var(--about-heading-margin-bottom-mobile, 10px);
    }

    .about-product__description {
      font-size: var(--about-text-size-mobile, 14px);
    }
  }

  @media screen and (max-width: 480px) {
    .about-product {
      padding-bottom: var(--about-padding-bottom-small, 18px);
      margin-bottom: var(--about-margin-bottom-small, 18px);
    }

    .about-product__heading {
      font-size: var(--about-heading-size-small, 15px);
      margin-bottom: var(--about-heading-margin-bottom-small, 8px);
    }

    .about-product__description {
      font-size: var(--about-text-size-small, 13px);
    }
  }
{% endstylesheet %}

{% schema %}
{
  "name": "About Product",
  "settings": [
    {
      "type": "header",
      "content": "Content"
    },
    {
      "type": "text",
      "id": "heading",
      "label": "Heading",
      "default": "About the product"
    },
    {
      "type": "textarea",
      "id": "description",
      "label": "Description",
      "default": "It's all about reducing fatigue and improving comfort! Power Walking & Hiking Upsteps smooth out the natural heel-to-toe 'roll' your feet make as you walk. They're individually designed for you from durable materials to prevent damage and reduce or eliminate pain."
    },
    {
      "type": "header",
      "content": "Styling"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background color"
    },
    {
      "type": "color",
      "id": "heading_color",
      "label": "Heading color"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text color"
    },
    {
      "type": "color",
      "id": "border_bottom_color",
      "label": "Bottom border color"
    },
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "range",
      "id": "border_radius",
      "label": "Border radius",
      "min": 0,
      "max": 20,
      "step": 1,
      "unit": "px",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding_vertical",
      "label": "Vertical padding",
      "min": 0,
      "max": 40,
      "step": 2,
      "unit": "px",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding_horizontal",
      "label": "Horizontal padding",
      "min": 0,
      "max": 40,
      "step": 2,
      "unit": "px",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "label": "Bottom padding",
      "min": 0,
      "max": 60,
      "step": 2,
      "unit": "px",
      "default": 24
    },
    {
      "type": "range",
      "id": "margin_top",
      "label": "Top margin",
      "min": 0,
      "max": 60,
      "step": 4,
      "unit": "px",
      "default": 0
    },
    {
      "type": "range",
      "id": "margin_bottom",
      "label": "Bottom margin",
      "min": 0,
      "max": 60,
      "step": 4,
      "unit": "px",
      "default": 24
    },
    {
      "type": "range",
      "id": "border_bottom_width",
      "label": "Bottom border width",
      "min": 0,
      "max": 5,
      "step": 1,
      "unit": "px",
      "default": 1
    },
    {
      "type": "header",
      "content": "Typography"
    },
    {
      "type": "range",
      "id": "heading_size",
      "label": "Heading size",
      "min": 14,
      "max": 28,
      "step": 1,
      "unit": "px",
      "default": 18
    },
    {
      "type": "range",
      "id": "heading_weight",
      "label": "Heading weight",
      "min": 300,
      "max": 800,
      "step": 100,
      "default": 600
    },
    {
      "type": "range",
      "id": "heading_margin_bottom",
      "label": "Heading bottom margin",
      "min": 4,
      "max": 30,
      "step": 2,
      "unit": "px",
      "default": 12
    },
    {
      "type": "range",
      "id": "text_size",
      "label": "Text size",
      "min": 12,
      "max": 20,
      "step": 1,
      "unit": "px",
      "default": 15
    },
    {
      "type": "range",
      "id": "text_weight",
      "label": "Text weight",
      "min": 300,
      "max": 700,
      "step": 100,
      "default": 400
    },
    {
      "type": "range",
      "id": "text_line_height",
      "label": "Text line height",
      "min": 1.2,
      "max": 2.0,
      "step": 0.1,
      "default": 1.6
    }
  ]
}
{% endschema %}

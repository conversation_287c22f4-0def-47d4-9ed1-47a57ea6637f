

{% render 'card-gallery' %}

{% schema %}
{
  "name": "t:names.product_media",
  "tag": null,
  "settings": [
    {
      "type": "paragraph",
      "content": "t:content.resource_reference_product_media"
    },
    {
      "type": "select",
      "id": "image_ratio",
      "options": [
        {
          "value": "adapt",
          "label": "t:options.auto"
        },
        {
          "value": "portrait",
          "label": "t:options.portrait"
        },
        {
          "value": "square",
          "label": "t:options.square"
        },
        {
          "value": "landscape",
          "label": "t:options.landscape"
        }
      ],
      "default": "adapt",
      "label": "t:settings.aspect_ratio"
    },
    {
      "type": "select",
      "id": "constrain_to_viewport",
      "options": [
        {
          "value": "",
          "label": "t:options.off"
        },
        {
          "value": "contain",
          "label": "t:options.maintain_aspect_ratio"
        },
        {
          "value": "cover",
          "label": "t:options.crop_to_fit"
        }
      ],
      "default": "contain",
      "label": "t:settings.limit_media_to_screen_height",
      "visible_if": "{{ block.settings.image_ratio == 'adapt' }}"
    }
  ]
}
{% endschema %}

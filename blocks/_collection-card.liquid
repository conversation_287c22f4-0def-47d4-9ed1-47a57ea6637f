

{% assign collection = closest.collection %}

{% style %}
  {% if request.visual_preview_mode %}
    .collection-card {
      min-width: 250px;
    }
  {% endif %}
{% endstyle %}

{% capture card_image %}
  {% content_for 'block',
    type: '_collection-card-image',
    id: 'collection-card-image',
    closest.collection: collection,
    parent_block_id: block.id %}
{% endcapture %}

{% capture children %}
  {% content_for 'blocks', closest.collection: collection %}
{% endcapture %}

{% render 'collection-card',
  card_image: card_image,
  children: children,
  block: block,
  collection: collection,
  section: section
%}

{% schema %}
{
  "name": "t:names.collection_card",
  "blocks": [
    {
      "type": "text"
    },
    {
      "type": "spacer"
    },
    {
      "type": "button"
    },
    {
      "type": "group"
    },
    {
      "type": "collection-title"
    }
  ],
  "tag": null,
  "settings": [
    {
      "type": "paragraph",
      "content": "t:content.resource_reference_collection_card"
    },
    {
      "type": "header",
      "content": "t:content.text"
    },
    {
      "type": "select",
      "id": "placement",
      "label": "t:settings.placement",
      "options": [
        {
          "value": "on_image",
          "label": "t:options.on_image"
        },
        {
          "value": "below_image",
          "label": "t:options.below_image"
        }
      ]
    },
    {
      "type": "select",
      "id": "horizontal_alignment",
      "label": "t:settings.alignment",
      "options": [
        {
          "value": "flex-start",
          "label": "t:options.left"
        },
        {
          "value": "center",
          "label": "t:options.center"
        },
        {
          "value": "flex-end",
          "label": "t:options.right"
        }
      ],
      "default": "flex-start"
    },
    {
      "type": "select",
      "id": "vertical_alignment",
      "label": "t:settings.position",
      "options": [
        {
          "value": "flex-start",
          "label": "t:options.top"
        },
        {
          "value": "center",
          "label": "t:options.center"
        },
        {
          "value": "flex-end",
          "label": "t:options.bottom"
        }
      ],
      "default": "center",
      "visible_if": "{{ block.settings.placement == \"on_image\" }}"
    },
    {
      "type": "range",
      "id": "collection_card_gap",
      "label": "t:settings.vertical_gap",
      "min": 0,
      "max": 50,
      "step": 1,
      "unit": "px",
      "default": 8
    },
    {
      "type": "header",
      "content": "t:content.appearance"
    },
    {
      "type": "checkbox",
      "id": "inherit_color_scheme",
      "label": "t:settings.inherit_color_scheme",
      "default": true
    },
    {
      "type": "color_scheme",
      "id": "color_scheme",
      "label": "t:settings.color_scheme",
      "default": "scheme-1",
      "visible_if": "{{ block.settings.inherit_color_scheme == false }}"
    },
    {
      "type": "select",
      "id": "border",
      "label": "t:settings.borders",
      "options": [
        {
          "value": "none",
          "label": "t:options.none"
        },
        {
          "value": "solid",
          "label": "t:options.solid"
        }
      ],
      "default": "none"
    },
    {
      "type": "range",
      "id": "border_width",
      "min": 0,
      "max": 10,
      "step": 0.5,
      "unit": "px",
      "label": "t:settings.border_width",
      "default": 1,
      "visible_if": "{{ block.settings.border != 'none' }}"
    },
    {
      "type": "range",
      "id": "border_opacity",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "%",
      "label": "t:settings.border_opacity",
      "default": 100,
      "visible_if": "{{ block.settings.border != 'none' }}"
    },
    {
      "type": "range",
      "id": "border_radius",
      "label": "t:settings.border_radius",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px",
      "default": 0
    }
  ]
}
{% endschema %}

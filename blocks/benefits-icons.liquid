{% comment %}
  Benefits Icons Block - Display product benefits with icons in a row
  Designed for product pages to highlight key benefits with gray background
{% endcomment %}

<div
  class="benefits-icons"
  style="
    --benefits-background: {{ block.settings.background_color | default: '#f8f9fa' }};
    --benefits-border-radius: {{ block.settings.border_radius | default: 8 }}px;
    --benefits-padding-vertical: {{ block.settings.padding_vertical | default: 24 }}px;
    --benefits-padding-horizontal: {{ block.settings.padding_horizontal | default: 20 }}px;
    --benefits-margin-top: {{ block.settings.margin_top | default: 0 }}px;
    --benefits-margin-bottom: {{ block.settings.margin_bottom | default: 0 }}px;
    --benefits-icon-size: {{ block.settings.icon_size | default: 42 }}px;
    --benefits-gap: {{ block.settings.gap | default: 16 }}px;
    --benefits-title-size: {{ block.settings.title_size | default: 14 }}px;
    --benefits-title-weight: {{ block.settings.title_weight | default: 500 }};
    --benefits-title-color: {{ block.settings.title_color | default: 'rgba(var(--color-foreground), 1)' }};
    --benefits-emoji-size: {{ block.settings.emoji_size | default: 20 }}px;
    --benefits-emoji-color: {{ block.settings.emoji_color | default: 'rgba(var(--color-foreground), 0.8)' }};
    --benefits-alignment: {{ block.settings.alignment | default: 'space-between' }};
    background: var(--benefits-background);
    border-radius: var(--benefits-border-radius);
    padding: var(--benefits-padding-vertical) var(--benefits-padding-horizontal);
    margin-top: var(--benefits-margin-top);
    margin-bottom: var(--benefits-margin-bottom);
  "
  {{ block.shopify_attributes }}
>
  <div class="benefits-icons__row">
    {% if block.settings.benefit_1_title != blank %}
      <div class="benefits-icons__item">
        {% if block.settings.benefit_1_icon != blank %}
          <div class="benefits-icons__icon">
            <img src="{{ block.settings.benefit_1_icon | image_url: width: 42 }}" alt="{{ block.settings.benefit_1_title }}" width="42" height="42" loading="lazy">
          </div>
        {% elsif block.settings.benefit_1_emoji != blank %}
          <div class="benefits-icons__icon benefits-icons__icon--emoji">
            {{ block.settings.benefit_1_emoji }}
          </div>
        {% endif %}
        <div class="benefits-icons__title">{{ block.settings.benefit_1_title }}</div>
      </div>
    {% endif %}

    {% if block.settings.benefit_2_title != blank %}
      <div class="benefits-icons__item">
        {% if block.settings.benefit_2_icon != blank %}
          <div class="benefits-icons__icon">
            <img src="{{ block.settings.benefit_2_icon | image_url: width: 42 }}" alt="{{ block.settings.benefit_2_title }}" width="42" height="42" loading="lazy">
          </div>
        {% elsif block.settings.benefit_2_emoji != blank %}
          <div class="benefits-icons__icon benefits-icons__icon--emoji">
            {{ block.settings.benefit_2_emoji }}
          </div>
        {% endif %}
        <div class="benefits-icons__title">{{ block.settings.benefit_2_title }}</div>
      </div>
    {% endif %}

    {% if block.settings.benefit_3_title != blank %}
      <div class="benefits-icons__item">
        {% if block.settings.benefit_3_icon != blank %}
          <div class="benefits-icons__icon">
            <img src="{{ block.settings.benefit_3_icon | image_url: width: 42 }}" alt="{{ block.settings.benefit_3_title }}" width="42" height="42" loading="lazy">
          </div>
        {% elsif block.settings.benefit_3_emoji != blank %}
          <div class="benefits-icons__icon benefits-icons__icon--emoji">
            {{ block.settings.benefit_3_emoji }}
          </div>
        {% endif %}
        <div class="benefits-icons__title">{{ block.settings.benefit_3_title }}</div>
      </div>
    {% endif %}
  </div>
</div>

{% stylesheet %}
  .benefits-icons {
    width: 100%;
  }

  .benefits-icons__row {
    display: flex;
    justify-content: var(--benefits-alignment);
    align-items: flex-start;
    gap: var(--benefits-gap);
    flex-wrap: wrap;
  }

  .benefits-icons__item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    flex: 1;
    min-width: 0;
  }

  .benefits-icons__icon {
    width: var(--benefits-icon-size);
    height: var(--benefits-icon-size);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 8px;
    flex-shrink: 0;
  }

  .benefits-icons__icon img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }

  .benefits-icons__icon--emoji {
    font-size: var(--benefits-emoji-size);
    color: var(--benefits-emoji-color);
  }

  .benefits-icons__title {
    font-size: var(--benefits-title-size);
    font-weight: var(--benefits-title-weight);
    color: var(--benefits-title-color);
    line-height: 1.3;
    text-align: center;
  }

  /* Mobile responsiveness */
  @media screen and (max-width: 768px) {
    .benefits-icons {
      padding: var(--benefits-padding-vertical-mobile, 20px) var(--benefits-padding-horizontal-mobile, 16px);
      border-radius: var(--benefits-border-radius-mobile, 6px);
    }

    .benefits-icons__row {
      gap: var(--benefits-gap-mobile, 20px);
      justify-content: var(--benefits-alignment-mobile, space-around);
    }

    .benefits-icons__item {
      flex: 1 0 30%;
      min-width: 80px;
    }

    .benefits-icons__icon {
      width: var(--benefits-icon-size-mobile, 38px);
      height: var(--benefits-icon-size-mobile, 38px);
      margin-bottom: 6px;
    }

    .benefits-icons__icon--emoji {
      font-size: var(--benefits-emoji-size-mobile, 18px);
    }

    .benefits-icons__title {
      font-size: var(--benefits-title-size-mobile, 13px);
    }
  }

  @media screen and (max-width: 480px) {
    .benefits-icons {
      padding: var(--benefits-padding-vertical-small, 18px) var(--benefits-padding-horizontal-small, 14px);
    }

    .benefits-icons__row {
      gap: var(--benefits-gap-small, 16px);
    }

    .benefits-icons__icon {
      width: var(--benefits-icon-size-small, 32px);
      height: var(--benefits-icon-size-small, 32px);
    }

    .benefits-icons__icon--emoji {
      font-size: var(--benefits-emoji-size-small, 16px);
    }

    .benefits-icons__title {
      font-size: var(--benefits-title-size-small, 12px);
    }
  }
{% endstylesheet %}

{% schema %}
{
  "name": "Benefits Icons",
  "settings": [
    {
      "type": "header",
      "content": "Benefit 1"
    },
    {
      "type": "text",
      "id": "benefit_1_title",
      "label": "Benefit 1 title",
      "default": "180-day money-back"
    },
    {
      "type": "image_picker",
      "id": "benefit_1_icon",
      "label": "Benefit 1 icon image"
    },
    {
      "type": "text",
      "id": "benefit_1_emoji",
      "label": "Benefit 1 emoji (fallback)",
      "default": "✓"
    },
    {
      "type": "header",
      "content": "Benefit 2"
    },
    {
      "type": "text",
      "id": "benefit_2_title",
      "label": "Benefit 2 title",
      "default": "Free shipping"
    },
    {
      "type": "image_picker",
      "id": "benefit_2_icon",
      "label": "Benefit 2 icon image"
    },
    {
      "type": "text",
      "id": "benefit_2_emoji",
      "label": "Benefit 2 emoji (fallback)",
      "default": "🚚"
    },
    {
      "type": "header",
      "content": "Benefit 3"
    },
    {
      "type": "text",
      "id": "benefit_3_title",
      "label": "Benefit 3 title",
      "default": "FSA/HSA Eligible"
    },
    {
      "type": "image_picker",
      "id": "benefit_3_icon",
      "label": "Benefit 3 icon image"
    },
    {
      "type": "text",
      "id": "benefit_3_emoji",
      "label": "Benefit 3 emoji (fallback)",
      "default": "💳"
    },
    {
      "type": "header",
      "content": "Styling"
    },
    {
      "type": "color",
      "id": "background_color",
      "label": "Background color",
      "default": "#f8f9fa"
    },
    {
      "type": "color",
      "id": "title_color",
      "label": "Title color"
    },
    {
      "type": "color",
      "id": "emoji_color",
      "label": "Emoji color"
    },
    {
      "type": "header",
      "content": "Layout"
    },
    {
      "type": "select",
      "id": "alignment",
      "label": "Icons alignment",
      "options": [
        {
          "value": "flex-start",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "space-between",
          "label": "Space between"
        },
        {
          "value": "space-around",
          "label": "Space around"
        }
      ],
      "default": "space-between"
    },
    {
      "type": "range",
      "id": "border_radius",
      "label": "Border radius",
      "min": 0,
      "max": 20,
      "step": 1,
      "unit": "px",
      "default": 8
    },
    {
      "type": "range",
      "id": "padding_vertical",
      "label": "Vertical padding",
      "min": 8,
      "max": 40,
      "step": 2,
      "unit": "px",
      "default": 24
    },
    {
      "type": "range",
      "id": "padding_horizontal",
      "label": "Horizontal padding",
      "min": 8,
      "max": 40,
      "step": 2,
      "unit": "px",
      "default": 20
    },
    {
      "type": "range",
      "id": "margin_top",
      "label": "Top margin",
      "min": 0,
      "max": 60,
      "step": 4,
      "unit": "px",
      "default": 0
    },
    {
      "type": "range",
      "id": "margin_bottom",
      "label": "Bottom margin",
      "min": 0,
      "max": 60,
      "step": 4,
      "unit": "px",
      "default": 0
    },
    {
      "type": "range",
      "id": "gap",
      "label": "Gap between items",
      "min": 8,
      "max": 40,
      "step": 2,
      "unit": "px",
      "default": 16
    },
    {
      "type": "range",
      "id": "icon_size",
      "label": "Icon size",
      "min": 24,
      "max": 80,
      "step": 2,
      "unit": "px",
      "default": 42
    },
    {
      "type": "header",
      "content": "Typography"
    },
    {
      "type": "range",
      "id": "title_size",
      "label": "Title size",
      "min": 10,
      "max": 18,
      "step": 1,
      "unit": "px",
      "default": 14
    },
    {
      "type": "range",
      "id": "title_weight",
      "label": "Title weight",
      "min": 300,
      "max": 800,
      "step": 100,
      "default": 500
    },
    {
      "type": "range",
      "id": "emoji_size",
      "label": "Emoji size",
      "min": 16,
      "max": 32,
      "step": 2,
      "unit": "px",
      "default": 20
    }
  ]
}
{% endschema %}
